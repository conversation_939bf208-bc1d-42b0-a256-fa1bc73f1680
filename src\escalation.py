import os
import numpy as np
import pandas as pd
from pathlib import Path
from sklearn.preprocessing import StandardScaler, MinMaxScaler

# Importa seus dados já integrados/limpos
# Usamos o dataloader SEM o preprocess, pois aqui queremos as colunas originais
import dataloader

# 0) Seleção das 3 variáveis
V1 = "Valor Total da Prestação do Serviço"  # Robust
V2 = "Valor total da carga"  # Standard
V3 = "Quantidade de Carga 01"  # MinMax

cols = [V1, V2, V3]

# Combina treino e teste para estatísticas do "conjunto completo"
df_full = pd.concat([dataloader.train_data, dataloader.test_data], axis=0, ignore_index=True)

# Garante que as 3 variáveis são numéricas
for c in cols:
    if c in df_full.columns:
        df_full[c] = pd.to_numeric(df_full[c], errors="coerce")

# Remove linhas totalmente nulas nas 3 variáveis
df_full = df_full.dropna(subset=cols, how="any").reset_index(drop=True)

# 1) Estatísticas
def pop_std(x: pd.Series) -> float:
    return float(np.std(x.values, ddof=0))

stats = []
for c in cols:
    s = df_full[c].astype(float)
    stats.append({
        "Variável": c,
        "min": float(s.min()),
        "q1": float(s.quantile(0.25)),
        "mediana": float(s.median()),
        "q3": float(s.quantile(0.75)),
        "max": float(s.max()),
        "media": float(s.mean()),
        "sigma_pop": pop_std(s),
        "iqr": float(s.quantile(0.75) - s.quantile(0.25)),
    })
stats_df = pd.DataFrame(stats, columns=["Variável","min","q1","mediana","q3","max","media","sigma_pop","iqr"])

print(stats_df[["Variável","min","max","media","sigma_pop"]].to_markdown(index=False))

# 2) Equações com constantes
def eq_robust(var, mediana, iqr):
    return f"**{var} (RobustScaler):**  x' = (x − {mediana:.6f}) / {iqr:.6f}"

def eq_standard(var, mu, sigma):
    return f"**{var} (StandardScaler):**  z = (x − {mu:.6f}) / {sigma:.6f}"

def eq_minmax(var, vmin, vmax):
    return f"**{var} (MinMax 0–1):**  x_norm = (x − {vmin:.6f}) / ({vmax:.6f} − {vmin:.6f})"

# Busca as estatísticas para cada variável
S1 = stats_df.loc[stats_df["Variável"]==V1].iloc[0]
S2 = stats_df.loc[stats_df["Variável"]==V2].iloc[0]
S3 = stats_df.loc[stats_df["Variável"]==V3].iloc[0]

print("\n### 6.4 (c) Equações do escalonamento (com constantes)")
print("-", eq_robust(V1, S1["mediana"], S1["iqr"]))
print("-", eq_standard(V2, S2["media"], S2["sigma_pop"]))
print("-", eq_minmax(V3, S3["min"], S3["max"]))

# 3) Aplicação dos escalonadores
df_scaled = df_full[[V1,V2,V3]].copy()

# Robust para V1
iqr = S1["iqr"] if S1["iqr"] != 0 else 1.0 
df_scaled[V1] = (df_full[V1] - S1["mediana"]) / iqr

# Standard para V2
sigma = S2["sigma_pop"] if S2["sigma_pop"] != 0 else 1.0
df_scaled[V2] = (df_full[V2] - S2["media"]) / sigma

# MinMax para V3
den = (S3["max"] - S3["min"]) if (S3["max"] - S3["min"]) != 0 else 1.0
df_scaled[V3] = (df_full[V3] - S3["min"]) / den

# 4) Tabelas comparativas (10 primeiras linhas)
head_orig = df_full[[V1,V2,V3]].head(10)
head_scaled = df_scaled[[V1,V2,V3]].head(10)

print("\n### 6.6 (e) Tabela – 10 primeiros registros (originais)")
print(head_orig.to_markdown(index=False))

print("\n### 6.6 (e) Tabela – 10 primeiros registros (escalonados)")
print(head_scaled.to_markdown(index=False))

# 5) (Opcional) Histogramas antes/depois
try:
    import matplotlib.pyplot as plt

    outdir = Path("assets/escalonamento")
    outdir.mkdir(parents=True, exist_ok=True)

    for c in [V1,V2,V3]:
        # antes
        plt.figure()
        df_full[c].plot.hist(bins=30)
        plt.title(f"Antes – {c}")
        plt.xlabel(c)
        plt.ylabel("Frequência")
        plt.tight_layout()
        plt.savefig(outdir / f"hist_before_{c.replace(' ','_')}.png", dpi=150)
        plt.close()

        # depois
        plt.figure()
        df_scaled[c].plot.hist(bins=30)
        plt.title(f"Depois – {c}")
        plt.xlabel(f"{c} (escalonado)")
        plt.ylabel("Frequência")
        plt.tight_layout()
        plt.savefig(outdir / f"hist_after_{c.replace(' ','_')}.png", dpi=150)
        plt.close()

    print("\n### 6.5 (d) Histogramas salvos em: assets/escalonamento/")
except Exception as e:
    print("\n(Obs.) Matplotlib não disponível ou erro ao salvar histogramas:", e)
