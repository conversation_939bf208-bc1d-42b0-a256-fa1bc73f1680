import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
from pathlib import Path


def _resolve_first_existing_path(candidates, base_dir="Data"):
    base_dir = Path(base_dir)
    
    print(f"📂 Procurando arquivos em: {base_dir.resolve()}")
    print("Arquivos disponíveis:")
    for f in base_dir.glob("*"):
        print(" -", f.name)

    for candidate in candidates:
        candidate_path = base_dir / candidate
        if candidate_path.exists():
            print(f"✅ Arquivo encontrado: {candidate_path}")
            return candidate_path

    raise FileNotFoundError(
        f"Nenhum dos arquivos foi encontrado em {base_dir.resolve()}: {candidates}"
    )

def limpar_dataset(df):
    if "Data de Emissão" in df.columns:
        df["Data de Emissão"] = pd.to_datetime(
            df["Data de Emissão"], errors="coerce", dayfirst=True
        )

    for col in df.select_dtypes(include="object"):
        if df[col].str.contains(",", regex=False).any():
            df[col] = df[col].str.replace(".", "", regex=False)
            df[col] = df[col].str.replace(",", ".", regex=False)
            try:
                df[col] = pd.to_numeric(df[col])
            except Exception:
                pass

    df = df.drop_duplicates()
    df = df.dropna(subset=["Chave de Acesso"])
    df = df.fillna(0)
    return df


def criar_features(df):
    if "Data de Emissão" in df.columns:
        df["Ano"] = df["Data de Emissão"].dt.year
        df["Mes"] = df["Data de Emissão"].dt.month
        df["Dia"] = df["Data de Emissão"].dt.day
        df["DiaSemana"] = df["Data de Emissão"].dt.dayofweek
        df["Safra"] = df["Ano"]
        df.loc[df["Mes"] < 7, "Safra"] = df["Ano"] - 1

    if "UF Origem" in df.columns:
        dummies = pd.get_dummies(df["UF Origem"], prefix="UFOrigem")
        df = pd.concat([df, dummies], axis=1)
    return df


def load_cte_data(filepath):
    df = pd.read_csv(filepath, sep=";", encoding="utf-8")
    df["Valor CT-e"] = (
        df["Valor CT-e"]
        .astype(str)
        .str.replace(".", "")
        .str.replace(",", ".")
        .astype(float)
    )
    df["Data da Emissão"] = pd.to_datetime(df["Data da Emissão"], format="%d/%m/%Y")
    df["Ano_Mes"] = df["Data da Emissão"].dt.to_period("M")
    df["Mes"] = df["Data da Emissão"].dt.month
    return df


cte_path = _resolve_first_existing_path([
    "CTE-22-23-24(in).csv",
    "CTE-24-25(in).csv"
])

df = pd.read_csv(cte_path, sep=";", encoding="utf-8")
df = limpar_dataset(df)
df = criar_features(df)
df.head()


df.describe(include="all").transpose()


import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns

# Configurações estéticas gerais do Seaborn
sns.set(style="whitegrid")


def load_cte_data(filepath):
    df = pd.read_csv(filepath, sep=";", encoding="utf-8")

    # Normaliza nomes das colunas (remove espaços extras)
    df.columns = df.columns.str.strip()

    # Resolve coluna de valor com nomes alternativos
    valor_aliases = [
        "Valor CT-e",
        "Valor do CT-e",
        "Valor do Frete",
        "Valor total da carga",
        "Valor Total da Prestação do Serviço",
        "Valor Total da Prestacao do Servico",
    ]
    valor_col = next((c for c in valor_aliases if c in df.columns), None)

    if valor_col is not None:
        df["Valor CT-e"] = (
            df[valor_col]
            .astype(str)
            .str.replace(".", "", regex=False)
            .str.replace(",", ".", regex=False)
        )
        # Converte para float apenas quando possível
        df["Valor CT-e"] = pd.to_numeric(df["Valor CT-e"], errors="coerce")
    else:
        # Cria a coluna vazia quando não existir no CSV
        df["Valor CT-e"] = pd.Series([None] * len(df), dtype="float64")
        print("[aviso] Coluna de valor não encontrada. 'Valor CT-e' criado vazio.")

    # Trata datas com nomes alternativos
    data_aliases = ["Data da Emissão", "Data de Emissão"]
    data_col = next((c for c in data_aliases if c in df.columns), None)
    if data_col is not None:
        df[data_col] = pd.to_datetime(df[data_col], format="%d/%m/%Y", errors="coerce")
        df["Ano_Mes"] = df[data_col].dt.to_period("M")
        df["Mes"] = df[data_col].dt.month
    else:
        print("[aviso] Coluna de data não encontrada. 'Ano_Mes' e 'Mes' não serão gerados.")

    return df

def plot_evolucao_temporal(df):
    has_valor = ("Valor CT-e" in df.columns) and df["Valor CT-e"].notna().any()

    if has_valor:
        monthly_stats = df.groupby("Ano_Mes").agg({"Valor CT-e": ["mean", "count"]}).round(2)
        fig, ax1 = plt.subplots(figsize=(15, 8))
        color = "tab:blue"
        ax1.set_xlabel("Período (Ano-Mês)")
        ax1.set_ylabel("Valor Médio do Frete (R$)", color=color)
        ax1.plot(
            monthly_stats.index.astype(str),
            monthly_stats[("Valor CT-e", "mean")],
            color=color,
            linewidth=2.5,
            marker="o",
        )
        ax1.tick_params(axis="y", labelcolor=color)
        ax1.tick_params(axis="x", rotation=45)

        ax2 = ax1.twinx()
        color = "tab:orange"
        ax2.set_ylabel("Número de Operações por Mês", color=color)
        ax2.plot(
            monthly_stats.index.astype(str),
            monthly_stats[("Valor CT-e", "count")],
            color=color,
            linewidth=2.5,
            marker="s",
            alpha=0.7,
        )
        ax2.tick_params(axis="y", labelcolor=color)
        plt.title(
            "Evolução Temporal dos Fretes CTE (2022-2024)\nValor Médio vs Volume de Operações",
            fontsize=14,
            fontweight="bold",
            pad=20,
        )
    else:
        monthly_counts = df.groupby("Ano_Mes").size()
        fig, ax = plt.subplots(figsize=(15, 8))
        ax.set_xlabel("Período (Ano-Mês)")
        ax.set_ylabel("Número de Operações por Mês")
        ax.plot(
            monthly_counts.index.astype(str),
            monthly_counts.values,
            color="tab:orange",
            linewidth=2.5,
            marker="s",
        )
        ax.tick_params(axis="x", rotation=45)
        plt.title(
            "Evolução Temporal dos Fretes CTE (2022-2024)\nVolume de Operações",
            fontsize=14,
            fontweight="bold",
            pad=20,
        )

    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def plot_comparacao_estados(df):
    col_origem = "UF do início de transporte"
    col_dest = "UF do fim do transporte"

    top_estados_origem = df[col_origem].value_counts().head(8).index
    top_estados_destino = df[col_dest].value_counts().head(8).index

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    has_valor = ("Valor CT-e" in df.columns) and df["Valor CT-e"].notna().any()

    df_origem = df[df[col_origem].isin(top_estados_origem)]
    df_destino = df[df[col_dest].isin(top_estados_destino)]

    if has_valor:
        origem_stats = df_origem.groupby(col_origem)["Valor CT-e"].mean().sort_values()
        ax1.barh(origem_stats.index, origem_stats.values, color="lightcoral", alpha=0.8)
        ax1.set_xlabel("Valor Médio do Frete (R$)")
        ax1.set_ylabel("Estado de Origem")
        ax1.set_title("Valor Médio por Estado de Origem", fontweight="bold")

        destino_stats = df_destino.groupby(col_dest)["Valor CT-e"].mean().sort_values()
        ax2.barh(destino_stats.index, destino_stats.values, color="lightblue", alpha=0.8)
        ax2.set_xlabel("Valor Médio do Frete (R$)")
        ax2.set_ylabel("Estado de Destino")
        ax2.set_title("Valor Médio por Estado de Destino", fontweight="bold")
    else:
        origem_counts = df_origem[col_origem].value_counts().sort_values()
        ax1.barh(origem_counts.index, origem_counts.values, color="lightcoral", alpha=0.8)
        ax1.set_xlabel("Número de Operações")
        ax1.set_ylabel("Estado de Origem")
        ax1.set_title("Volume por Estado de Origem", fontweight="bold")

        destino_counts = df_destino[col_dest].value_counts().sort_values()
        ax2.barh(destino_counts.index, destino_counts.values, color="lightblue", alpha=0.8)
        ax2.set_xlabel("Número de Operações")
        ax2.set_ylabel("Estado de Destino")
        ax2.set_title("Volume por Estado de Destino", fontweight="bold")

    ax1.grid(axis="x", alpha=0.3)
    ax2.grid(axis="x", alpha=0.3)
    plt.tight_layout()
    plt.show()


def plot_sazonalidade_mensal(df):
    has_valor = ("Valor CT-e" in df.columns) and df["Valor CT-e"].notna().any()

    plt.figure(figsize=(12, 6))
    if has_valor:
        monthly_avg = df.groupby("Mes")["Valor CT-e"].mean()
        plt.plot(
            monthly_avg.index,
            monthly_avg.values,
            marker="o",
            linewidth=3,
            markersize=10,
            color="darkblue",
            markerfacecolor="lightblue",
            markeredgewidth=2,
        )
        plt.ylabel("Valor Médio do Frete (R$)")
        plt.title(
            "Sazonalidade - Valor Médio por Mês\n(Dados combinados 2022-2024)",
            fontweight="bold",
            fontsize=14,
        )
    else:
        monthly_counts = df.groupby("Mes").size()
        plt.plot(
            monthly_counts.index,
            monthly_counts.values,
            marker="o",
            linewidth=3,
            markersize=10,
            color="darkblue",
            markerfacecolor="lightblue",
            markeredgewidth=2,
        )
        plt.ylabel("Número de Operações")
        plt.title(
            "Sazonalidade - Volume por Mês\n(Dados combinados 2022-2024)",
            fontweight="bold",
            fontsize=14,
        )

    plt.xlabel("Mês do Ano")
    meses = ["Jan", "Fev", "Mar", "Abr", "Mai", "Jun", "Jul", "Ago", "Set", "Out", "Nov", "Dez"]
    plt.xticks(range(1, 13), meses)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


from pathlib import Path

# Use o caminho já resolvido e existente
# Fonte única para os gráficos
df_plot = load_cte_data(cte_path)
# Ajusta o caminho para a pasta correta "data"
cte_path = Path("data") / cte_path.name
df_plot = load_cte_data(cte_path)
# Diagnóstico rápido
print("Linhas x Colunas:", df_plot.shape)
print("Colunas:", list(df_plot.columns))
print("Possui 'Valor CT-e'?:", "Valor CT-e" in df_plot.columns, "| N válidos:", df_plot.get("Valor CT-e").notna().sum() if "Valor CT-e" in df_plot.columns else 0)
print("Possui 'Ano_Mes'?:", "Ano_Mes" in df_plot.columns, "| N válidos:", df_plot.get("Ano_Mes").notna().sum() if "Ano_Mes" in df_plot.columns else 0)
print("Possui 'Mes'?:", "Mes" in df_plot.columns, "| N válidos:", df_plot.get("Mes").notna().sum() if "Mes" in df_plot.columns else 0)

# Recalcula datas se necessário
if ("Ano_Mes" not in df_plot.columns) or (df_plot["Ano_Mes"].isna().all()):
    for cand in ["Data da Emissão", "Data de Emissão"]:
        if cand in df_plot.columns:
            df_plot[cand] = pd.to_datetime(df_plot[cand], errors="coerce", dayfirst=True)
            df_plot["Ano_Mes"] = df_plot[cand].dt.to_period("M")
            df_plot["Mes"] = df_plot[cand].dt.month
            break

# Limpa linhas sem período para os gráficos temporais
df_plot = df_plot.copy()
if "Ano_Mes" in df_plot.columns:
    df_plot = df_plot[df_plot["Ano_Mes"].notna()]

print("Após ajustes - Linhas para gráficos temporais:", len(df_plot))
df_plot.head()

### 1. Importação das Bibliotecas e Dados do Dataloader (movido antes dos gráficos)
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler

# Carregar os dados combinados do dataloader (fallback para df_plot se não existir)
try:
    train_data = dataloader.train_data.copy()
    test_data = dataloader.test_data.copy()
except NameError:
    from sklearn.model_selection import train_test_split
    base_df = None
    if 'df_plot' in globals():
        base_df = df_plot.copy()
    elif 'df' in globals():
        base_df = df.copy()
    else:
        raise NameError("Nem 'dataloader' nem 'df_plot'/'df' estão definidos para compor os conjuntos de treino/teste.")
    train_data, test_data = train_test_split(base_df, test_size=0.2, random_state=42, shuffle=True)

print("Amostra dos dados de treino (brutos):")
display(train_data.head())


### 2. Tratamento de Missing Values (movido antes dos gráficos)

def tratar_missing_values(df):
    # Preencher numéricos com a mediana
    for col in df.select_dtypes(include=['float64', 'int64']).columns:
        df[col] = df[col].fillna(df[col].median())
    # Preencher categóricos com "Desconhecido"
    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].fillna("Desconhecido")
    return df

train_data = tratar_missing_values(train_data)
test_data = tratar_missing_values(test_data)

print("Missing values tratados!")


### 3. Tratamento de Outliers e Normalização (movido antes dos gráficos)

def escalar_variaveis(df, robust=True, standard=True):
    num_cols = df.select_dtypes(include=['float64', 'int64']).columns
    df_scaled = df.copy()

    if robust:
        print("\nAplicando RobustScaler (reduzir impacto de outliers)...")
        robust_scaler = RobustScaler()
        df_scaled[num_cols] = robust_scaler.fit_transform(df_scaled[num_cols])

    if standard:
        print("\nAplicando StandardScaler (distribuição normalizada)...")
        standard_scaler = StandardScaler()
        df_scaled[num_cols] = standard_scaler.fit_transform(df_scaled[num_cols])

    return df_scaled

train_data = escalar_variaveis(train_data, robust=True, standard=True)
test_data = escalar_variaveis(test_data, robust=True, standard=True)


### 4. Resultados após pré-processamento (movido antes dos gráficos)

print("\nPré-processamento concluído com sucesso.")
print("Amostra de dados de treino após normalização:")
display(train_data.head())

print("\nColunas numéricas normalizadas:")
print(train_data.select_dtypes(include=['float64', 'int64']).columns.tolist())


plot_evolucao_temporal(df_plot)


plot_comparacao_estados(df_plot)


plot_sazonalidade_mensal(df_plot)


### 1. Importação das Bibliotecas e Dados do Dataloader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler

# Carregar os dados combinados do dataloader (fallback para df_plot se não existir)
try:
    train_data = explorationloader.train_data.copy()
    test_data = explorationloader.test_data.copy()
except NameError:
    from sklearn.model_selection import train_test_split
    base_df = None
    if 'df_plot' in globals():
        base_df = df_plot.copy()
    elif 'df' in globals():
        base_df = df.copy()
    else:
        raise NameError("Nem 'dataloader' nem 'df_plot'/'df' estão definidos para compor os conjuntos de treino/teste.")
    train_data, test_data = train_test_split(base_df, test_size=0.2, random_state=42, shuffle=True)

print("Amostra dos dados de treino (brutos):")
display(train_data.head())


### 2. Tratamento de Missing Values

def tratar_missing_values(df):
    # Preencher numéricos com a mediana
    for col in df.select_dtypes(include=['float64', 'int64']).columns:
        df[col] = df[col].fillna(df[col].median())
    # Preencher categóricos com "Desconhecido"
    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].fillna("Desconhecido")
    return df

train_data = tratar_missing_values(train_data)
test_data = tratar_missing_values(test_data)

print("Missing values tratados!")


### 3. Tratamento de Outliers e Normalização

def escalar_variaveis(df, robust=True, standard=True):
    num_cols = df.select_dtypes(include=['float64', 'int64']).columns
    df_scaled = df.copy()

    if robust:
        print("\nAplicando RobustScaler (reduzir impacto de outliers)...")
        robust_scaler = RobustScaler()
        df_scaled[num_cols] = robust_scaler.fit_transform(df_scaled[num_cols])

    if standard:
        print("\nAplicando StandardScaler (distribuição normalizada)...")
        standard_scaler = StandardScaler()
        df_scaled[num_cols] = standard_scaler.fit_transform(df_scaled[num_cols])

    return df_scaled

train_data = escalar_variaveis(train_data, robust=True, standard=True)
test_data = escalar_variaveis(test_data, robust=True, standard=True)


### 4. Resultados após pré-processamento

print("\nPré-processamento concluído com sucesso.")
print("Amostra de dados de treino após normalização:")
display(train_data.head())

print("\nColunas numéricas normalizadas:")
print(train_data.select_dtypes(include=['float64', 'int64']).columns.tolist())
