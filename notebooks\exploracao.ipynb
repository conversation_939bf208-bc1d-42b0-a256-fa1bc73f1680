{"cells": [{"cell_type": "code", "execution_count": 2, "id": "d0f925ad", "metadata": {}, "outputs": [], "source": ["import sys\n", "import os\n", "sys.path.append(os.path.abspath(\"..\"))"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 5}