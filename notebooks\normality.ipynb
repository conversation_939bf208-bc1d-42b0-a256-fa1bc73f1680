{"cells": [{"cell_type": "code", "execution_count": 40, "id": "d4b15a51", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import shapiro\n", "\n", "# Configurações do matplotlib\n", "plt.style.use('default')\n", "%matplotlib inline\n", "\n"]}, {"cell_type": "code", "execution_count": 41, "id": "def85b24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["🚀 INICIANDO ANÁLISE DE NORMALIDADE\n", "==================================================\n", "\n", "📂 1. Carregando dados via loader.py...\n", "✅ Dados de treino carregados com sucesso! Dimensões: (145889, 16)\n", "Colunas disponíveis: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Data da Emissão', 'Fornecedor', 'Código Fornecedor', 'Cidade de fim do transporte', 'UF do fim do transporte', 'Cidade de início de transporte', 'UF do início de transporte', 'Produto', 'Valor Total da Prestação do Serviço', 'Valor do ICMS', 'Total Imposto', 'Valor total da carga', 'Tipo de Medida 01', 'Quantidade de Carga 01']\n"]}], "source": ["# Configurações do matplotlib\n", "plt.style.use('default')\n", "# %matplotlib inline  # Comentado para evitar erro fora do Jupyter\n", "\n", "print(\"🚀 INICIANDO ANÁLISE DE NORMALIDADE\")\n", "print(\"=\" * 50)\n", "\n", "# === 1. <PERSON><PERSON><PERSON> os dados do loader.py ===\n", "try:\n", "    print(\"\\n📂 1. Carregando dados via loader.py...\")\n", "    import loader\n", "    \n", "    # Carrega os dados de treino\n", "    df = loader.get_train_data()\n", "    print(f\"✅ Dados de treino carregados com sucesso! Dimensões: {df.shape}\")\n", "    print(f\"Colunas disponíveis: {list(df.columns)}\")\n", "    \n", "except ImportError as e:\n", "    print(\"❌ Erro ao importar loader.py:\")\n", "    print(f\"   {e}\")\n", "    print(\"\\n🔄 Tentando carregar diretamente do arquivo CSV...\")\n", "    \n", "    try:\n", "        df = pd.read_csv(\"Data/CTE-22-23-24(in).csv\", sep=\";\", encoding=\"utf-8\")\n", "        print(f\"✅ Arquivo CSV carregado diretamente! Dimensões: {df.shape}\")\n", "        \n", "        # Aplica a limpeza básica do dataset\n", "        print(\"🧹 Aplicando limpeza básica dos dados...\")\n", "        \n", "        # Converte data de emissão\n", "        if \"Data de Emissão\" in df.columns:\n", "            df[\"Data de Emissão\"] = pd.to_datetime(df[\"Data de Emissão\"], errors=\"coerce\", dayfirst=True)\n", "        \n", "        # Converte colunas numéricas com formato brasileiro\n", "        for col in df.select_dtypes(include=\"object\"):\n", "            if df[col].str.contains(\",\", regex=False).any():\n", "                df[col] = df[col].str.replace(\".\", \"\", regex=False)\n", "                df[col] = df[col].str.replace(\",\", \".\", regex=False)\n", "                try:\n", "                    df[col] = pd.to_numeric(df[col])\n", "                except Exception:\n", "                    pass\n", "        \n", "        # Remove duplicatas e linhas problemáticas\n", "        df = df.drop_duplicates()\n", "        if \"<PERSON>ve de <PERSON>sso\" in df.columns:\n", "            df = df.dropna(subset=[\"<PERSON><PERSON>sso\"])\n", "        df = df.fillna(0)\n", "        \n", "        print(f\"✅ Limpeza concluída! Dimensões finais: {df.shape}\")\n", "        \n", "    except FileNotFoundError:\n", "        print(\"❌ Arquivo não encontrado! Verifique se 'Data/CTE-22-23-24(in).csv' existe.\")\n", "        print(\"Tentando caminhos alternativos...\")\n", "        \n", "        # Tenta outros possíveis caminhos\n", "        caminhos_alternativos = [\n", "            \"CTE-22-23-24(in).csv\",\n", "            \"../Data/CTE-22-23-24(in).csv\",\n", "            \"data/CTE-22-23-24(in).csv\",\n", "            \"Data/CTE-22-23-24(in).csv\"\n", "        ]\n", "        \n", "        for caminho in caminhos_alternativos:\n", "            try:\n", "                df = pd.read_csv(caminho, sep=\";\", encoding=\"utf-8\")\n", "                print(f\"✅ Arquivo encontrado em: {caminho}\")\n", "                break\n", "            except FileNotFoundError:\n", "                continue\n", "        else:\n", "            raise FileNotFoundError(\"Não foi possível encontrar o arquivo CSV em nenhum local.\")\n", "            \n", "# Lista das colunas desejadas\n", "colunas_desejadas = [\n", "    \"Valor Total da Prestação do Serviço\",\n", "    \"Valor total da carga\",\n", "    \"Quantidade de Carga 01\"\n", "]"]}, {"cell_type": "code", "execution_count": 42, "id": "61e34c5d", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["✅ Colunas encontradas: ['Valor Total da Prestação do Serviço', 'Valor total da carga', 'Quantidade de Carga 01']\n"]}], "source": ["colunas_existentes = [col for col in colunas_desejadas if col in df.columns]\n", "colunas_nao_encontradas = [col for col in colunas_desejadas if col not in df.columns]\n", "\n", "print(f\"✅ Colunas encontradas: {colunas_existentes}\")\n", "if colunas_nao_encontradas:\n", "    print(f\"⚠️ Colunas não encontradas: {colunas_nao_encontradas}\")\n", "    \n", "    # Busca por colunas similares\n", "    print(\"\\n🔍 Procurando colunas similares...\")\n", "    todas_colunas = df.columns.tolist()\n", "    \n", "    for col_nao_encontrada in colunas_nao_encontradas:\n", "        palavras_chave = col_nao_encontrada.lower().split()\n", "        colunas_similares = []\n", "        \n", "        for col_df in todas_colunas:\n", "            if any(palavra in col_df.lower() for palavra in palavras_chave):\n", "                colunas_similares.append(col_df)\n", "        \n", "        if colunas_similares:\n", "            print(f\"  Colunas similares a '{col_nao_encontrada}': {colunas_similares}\")"]}, {"cell_type": "code", "execution_count": 43, "id": "586c9a7b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 Dataset com 3 variáveis selecionadas.\n", "\n", "🔧 3. Convertendo dados para formato numérico...\n", "  ✅ Valor Total da Prestação do Serviço convertida\n", "  ✅ Valor total da carga convertida\n", "  ✅ Quantidade de Carga 01 convertida\n", "\n", "🧹 4. <PERSON><PERSON><PERSON> dados...\n", "Valores nulos por coluna:\n", "  Valor Total da Prestação do Serviço: 133512 (91.52%)\n", "  Valor total da carga: 133512 (91.52%)\n", "  Quantidade de Carga 01: 133512 (91.52%)\n", "\n", "📈 Linhas originais: 145889\n", "📉 Linhas após limpeza: 12377\n", "🗑️ Linhas removidas: 133512\n"]}], "source": ["if len(colunas_existentes) == 0:\n", "    raise ValueError(\"Nenhuma das colunas necessárias foi encontrada no dataset!\")\n", "\n", "variaveis = df[colunas_existentes].copy()\n", "print(f\"📊 Dataset com {len(colunas_existentes)} variáveis selecionadas.\")\n", "\n", "# === 3. <PERSON><PERSON><PERSON> para nú<PERSON>o (tratar formato brasileiro) ===\n", "print(\"\\n🔧 3. Convertendo dados para formato numérico...\")\n", "\n", "def converter_para_numerico(serie):\n", "    \"\"\"Converte string em formato brasileiro para float\"\"\"\n", "    if serie.dtype == 'object':\n", "        # Remove pontos (separador de milhar) e troca vírgula por ponto (decimal)\n", "        serie_convertida = (serie.astype(str)\n", "                           .str.replace(\".\", \"\", regex=False)\n", "                           .str.replace(\",\", \".\", regex=False))\n", "        # Converte para numérico, colocando NaN para valores inválidos\n", "        return pd.to_numeric(serie_convertida, errors='coerce')\n", "    else:\n", "        return pd.to_numeric(serie, errors='coerce')\n", "\n", "# Aplica conversão em todas as colunas\n", "for coluna in variaveis.columns:\n", "    variaveis[coluna] = converter_para_numerico(variaveis[coluna])\n", "    print(f\"  ✅ {coluna} convertida\")\n", "\n", "# === 4. Verificar e remover valores nulos ===\n", "print(\"\\n🧹 4. Limpando dados...\")\n", "\n", "# Mostra estatísticas de valores nulos\n", "print(\"Valores nulos por coluna:\")\n", "nulos_por_coluna = variaveis.isnull().sum()\n", "for coluna, count in nulos_por_coluna.items():\n", "    print(f\"  {coluna}: {count} ({count/len(variaveis)*100:.2f}%)\")\n", "\n", "# Remove linhas com valores nulos\n", "linhas_originais = len(variaveis)\n", "variaveis = variaveis.dropna()\n", "linhas_finais = len(variaveis)\n", "\n", "print(f\"\\n📈 Linhas originais: {linhas_originais}\")\n", "print(f\"📉 Linhas após limpeza: {linhas_finais}\")\n", "print(f\"🗑️ Linhas removidas: {linhas_originais - linhas_finais}\")\n", "\n", "if linhas_finais == 0:\n", "    raise ValueError(\"Todos os dados foram removidos durante a limpeza!\")"]}, {"cell_type": "code", "execution_count": 44, "id": "80a0ce3a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== ESTATÍSTICAS DESCRITIVAS ===\n", "                                        Média   Mediana  <PERSON>  \\\n", "Valor Total da Prestação do Serviço   5812.25   5445.07        2096.11   \n", "Valor total da carga                  6814.41   4669.60       13881.16   \n", "Quantidade de Carga 01               42525.12  46830.00        7949.68   \n", "\n", "                                      <PERSON><PERSON><PERSON>  \n", "Valor Total da Prestação do Serviço     5.83   14250.00  \n", "Valor total da carga                    0.00  509203.98  \n", "Quantidade de Carga 01               1000.00   74380.00  \n", "\n", "📈 6. <PERSON><PERSON><PERSON> histogramas...\n"]}, {"data": {"image/png": "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************************************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", "text/plain": ["<Figure size 1500x1500 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["medidas = pd.DataFrame({\n", "    \"Média\": variaveis.mean().round(2),\n", "    \"Mediana\": variaveis.median().round(2),\n", "    \"Desvio <PERSON>\": variaveis.std().round(2),\n", "    \"Mínimo\": variaveis.min().round(2),\n", "    \"Máximo\": variaveis.max().round(2)\n", "})\n", "\n", "print(\"=== ESTATÍSTICAS DESCRITIVAS ===\")\n", "print(medidas)\n", "\n", "# === 6. <PERSON><PERSON><PERSON> histogramas ===\n", "print(\"\\n📈 6. Gerando histogramas...\")\n", "\n", "# Criar diretório para salvar gráficos\n", "outdir = Path(\"assets/analise_normalidade\")\n", "outdir.mkdir(parents=True, exist_ok=True)\n", "\n", "plt.figure(figsize=(15, 5*len(variaveis.columns)))\n", "\n", "for i, coluna in enumerate(variaveis.columns, 1):\n", "    plt.subplot(len(variaveis.columns), 1, i)\n", "    \n", "    # Plotar histograma\n", "    plt.hist(variaveis[coluna], bins=30, edgecolor=\"black\", alpha=0.7, color='skyblue')\n", "    plt.title(f\"Histograma - {coluna}\", fontsize=14, fontweight='bold')\n", "    plt.xlabel(coluna, fontsize=12)\n", "    plt.ylabel(\"Frequência\", fontsize=12)\n", "    plt.grid(axis=\"y\", alpha=0.3)\n", "    \n", "    # Adicionar linha da média e mediana\n", "    plt.axvline(variaveis[coluna].mean(), color='red', linestyle='--', \n", "                label=f'Média: {variaveis[coluna].mean():.2f}')\n", "    plt.axvline(variaveis[coluna].median(), color='green', linestyle='--', \n", "                label=f'Mediana: {variaveis[coluna].median():.2f}')\n", "    plt.legend()\n", "\n", "plt.tight_layout()\n", "plt.savefig(outdir / \"histogramas_normalidade.png\", dpi=300, bbox_inches='tight')\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 45, "id": "2505c35c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔬 7. Aplicando teste de normalidade (Shapiro-Wilk)...\n", "  Testando: Valor Total da Prestação do Serviço\n", "    ⚠️ <PERSON><PERSON> muito grande (12377), usando amostra aleatória de 5000\n", "    ✅ Concluído: Não Normal\n", "  Testando: <PERSON><PERSON> total da carga\n", "    ⚠️ <PERSON><PERSON> muito grande (12377), usando amostra aleatória de 5000\n", "    ✅ Concluído: Não Normal\n", "  Testando: Quantidade de Carga 01\n", "    ⚠️ <PERSON><PERSON> muito grande (12377), usando amostra aleatória de 5000\n", "    ✅ Concluído: Não Normal\n"]}], "source": ["print(\"\\n🔬 7. Aplicando teste de normalidade (Shapiro-Wilk)...\")\n", "\n", "resultados = {}\n", "for coluna in variaveis.columns:\n", "    print(f\"  Testando: {coluna}\")\n", "    \n", "    # <PERSON>ita a amostra para o teste (Shapiro<PERSON>Wilk funciona melhor com n < 5000)\n", "    amostra = variaveis[coluna]\n", "    if len(amostra) > 5000:\n", "        print(f\"    ⚠️ Amostra muito grande ({len(amostra)}), usando amostra aleatória de 5000\")\n", "        amostra = amostra.sample(5000, random_state=42)\n", "    \n", "    try:\n", "        stat, p = shapiro(amostra)\n", "        resultados[coluna] = {\n", "            \"Estatística\": round(stat, 6),\n", "            \"p-valor\": round(p, 6) if p > 1e-6 else \"<0.000001\",\n", "            \"<PERSON><PERSON><PERSON> da Amos<PERSON>\": len(amostra),\n", "            \"Conclusão\": \"Normal\" if p > 0.05 else \"Não Normal\"\n", "        }\n", "        print(f\"    ✅ Concluído: {resultados[coluna]['Conclusão']}\")\n", "    except Exception as e:\n", "        print(f\"    ❌ Erro no teste: {e}\")\n", "        resultados[coluna] = {\n", "            \"Estatística\": \"Erro\",\n", "            \"p-valor\": \"Erro\",\n", "            \"<PERSON><PERSON><PERSON> da Amos<PERSON>\": len(amostra),\n", "            \"Conclusão\": \"Teste falhou\"\n", "        }"]}, {"cell_type": "code", "execution_count": 46, "id": "7ed6027f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📋 8. Compilando resultados...\n", "\n", "================================================================================\n", "RESULTADOS DO TESTE DE NORMALIDADE (SHAPIRO-WILK)\n", "================================================================================\n", "                                    Estatística    p-valor <PERSON><PERSON>  \\\n", "Valor Total da Prestação do Serviço    0.967391  <0.000001               5000   \n", "Valor total da carga                   0.195729  <0.000001               5000   \n", "Quantidade de Carga 01                 0.805324  <0.000001               5000   \n", "\n", "                                      <PERSON><PERSON><PERSON><PERSON>  \n", "Valor Total da Prestação do Serviço  Não Normal  \n", "Valor total da carga                 Não Normal  \n", "Quantidade de Carga 01               Não Normal  \n", "================================================================================\n"]}], "source": ["print(\"\\n📋 8. Compilando resultados...\")\n", "\n", "tabela_resultados = pd.DataFrame(resultados).T\n", "\n", "print(\"\\n\" + \"=\"*80)\n", "print(\"RESULTADOS DO TESTE DE NORMALIDADE (SHAPIRO-WILK)\")\n", "print(\"=\"*80)\n", "print(tabela_resultados)\n", "print(\"=\"*80)"]}, {"cell_type": "code", "execution_count": 47, "id": "6324cf11", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", " 9. INTERPRETAÇÃO DOS RESULTADOS:\n", "--------------------------------------------------\n", "\n", "📊 Valor Total da Prestação do Serviço:\n", "   • Estatística W: 0.967391\n", "   • p-valor: <0.000001\n", "   • Conclusão: Não Normal\n", "   ❌ A variável NÃO SEGUE distribuição normal (p ≤ 0.05)\n", "\n", "📊 Valor total da carga:\n", "   • Estatística W: 0.195729\n", "   • p-valor: <0.000001\n", "   • Conclusão: Não Normal\n", "   ❌ A variável NÃO SEGUE distribuição normal (p ≤ 0.05)\n", "\n", "📊 Quantidade de Carga 01:\n", "   • Estatística W: 0.805324\n", "   • p-valor: <0.000001\n", "   • Conclusão: Não Normal\n", "   ❌ A variável NÃO SEGUE distribuição normal (p ≤ 0.05)\n", "\n", "==================================================\n", "📝 RESUMO:\n", "   • Variáveis com distribuição normal: 0\n", "   • Variáveis sem distribuição normal: 3\n"]}], "source": ["print(\"\\n 9. INTERPRETAÇÃO DOS RESULTADOS:\")\n", "print(\"-\" * 50)\n", "\n", "for coluna in variaveis.columns:\n", "    resultado = resultados[coluna]\n", "    print(f\"\\n📊 {coluna}:\")\n", "    print(f\"   • Estatística W: {resultado['Estatística']}\")\n", "    print(f\"   • p-valor: {resultado['p-valor']}\")\n", "    print(f\"   • Conclus<PERSON>: {resultado['Conclusão']}\")\n", "    \n", "    if resultado['Conclusão'] == \"Normal\":\n", "        print(f\"   ✅ A variável SEGUE distribuição normal (p > 0.05)\")\n", "    elif <PERSON>['<PERSON><PERSON><PERSON><PERSON>'] == \"Não Normal\":\n", "        print(f\"   ❌ A variável NÃO SEGUE distribuição normal (p ≤ 0.05)\")\n", "    else:\n", "        print(f\"   ⚠️ Teste não foi executado corretamente\")\n", "\n", "print(\"\\n\" + \"=\"*50)\n", "print(\"📝 RESUMO:\")\n", "normais = sum(1 for r in resultados.values() if r['Conclusão'] == \"Normal\")\n", "nao_normais = sum(1 for r in resultados.values() if r['Conclusão'] == \"Não Normal\")\n", "erros = sum(1 for r in resultados.values() if r['<PERSON><PERSON><PERSON><PERSON>'] == \"Teste falhou\")\n", "\n", "print(f\"   • Variáveis com distribuição normal: {normais}\")\n", "print(f\"   • Variáveis sem distribuição normal: {nao_normais}\")\n", "if erros > 0:\n", "    print(f\"   • Testes com erro: {erros}\")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}