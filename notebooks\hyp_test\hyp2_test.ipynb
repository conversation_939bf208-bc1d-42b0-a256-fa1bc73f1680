{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a72f3ada", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 2, "id": "06d28136", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hipótese 2: A distribuição do valor do frete é diferente entre os diferentes estados de destino?\n"]}], "source": ["print(\"Hipótese 2: A distribuição do valor do frete é diferente entre os diferentes estados de destino?\")"]}, {"cell_type": "code", "execution_count": 3, "id": "0e03d1c3", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 40\u001b[39m\n\u001b[32m     37\u001b[39m cte_file_path = \u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mCTE-22-23-24(in).csv\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     38\u001b[39m graneis_file_path = \u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGraneis-22-23-24(in).csv\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m40\u001b[39m df_final = \u001b[43mload_and_preprocess_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcte_file_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgraneis_file_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     42\u001b[39m \u001b[38;5;66;03m# Salvar o dataframe processado para uso posterior\u001b[39;00m\n\u001b[32m     43\u001b[39m df_final.to_csv(\u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mprocessed_freight_data.csv\u001b[39m\u001b[33m'\u001b[39m, index=\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 3\u001b[39m, in \u001b[36mload_and_preprocess_data\u001b[39m\u001b[34m(cte_file, graneis_file)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mload_and_preprocess_data\u001b[39m(cte_file, graneis_file):\n\u001b[32m      2\u001b[39m     \u001b[38;5;66;03m# <PERSON>egar os arquivos CSV\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m     df_cte = \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcte_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m;\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecimal\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m'\u001b[39;49m\u001b[33;43m,\u001b[39;49m\u001b[33;43m'\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m     df_graneis = pd.read_csv(graneis_file, sep=\u001b[33m'\u001b[39m\u001b[33m;\u001b[39m\u001b[33m'\u001b[39m, decimal=\u001b[33m'\u001b[39m\u001b[33m,\u001b[39m\u001b[33m'\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;66;03m# Renomear colunas para facilitar a junção e análise\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[39m, in \u001b[36mread_csv\u001b[39m\u001b[34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[39m\n\u001b[32m   1013\u001b[39m kwds_defaults = _refine_defaults_read(\n\u001b[32m   1014\u001b[39m     dialect,\n\u001b[32m   1015\u001b[39m     delimiter,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1022\u001b[39m     dtype_backend=dtype_backend,\n\u001b[32m   1023\u001b[39m )\n\u001b[32m   1024\u001b[39m kwds.update(kwds_defaults)\n\u001b[32m-> \u001b[39m\u001b[32m1026\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[39m, in \u001b[36m_read\u001b[39m\u001b[34m(filepath_or_buffer, kwds)\u001b[39m\n\u001b[32m    617\u001b[39m _validate_names(kwds.get(\u001b[33m\"\u001b[39m\u001b[33mnames\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m    619\u001b[39m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m620\u001b[39m parser = \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    622\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[32m    623\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m parser\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[39m, in \u001b[36mTextFileReader.__init__\u001b[39m\u001b[34m(self, f, engine, **kwds)\u001b[39m\n\u001b[32m   1617\u001b[39m     \u001b[38;5;28mself\u001b[39m.options[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m] = kwds[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m   1619\u001b[39m \u001b[38;5;28mself\u001b[39m.handles: IOHandles | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1620\u001b[39m \u001b[38;5;28mself\u001b[39m._engine = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[39m, in \u001b[36mTextFileReader._make_engine\u001b[39m\u001b[34m(self, f, engine)\u001b[39m\n\u001b[32m   1878\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[32m   1879\u001b[39m         mode += \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1880\u001b[39m \u001b[38;5;28mself\u001b[39m.handles = \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1881\u001b[39m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1882\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1883\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1884\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcompression\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1885\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmemory_map\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1886\u001b[39m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m=\u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1887\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding_errors\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstrict\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1888\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstorage_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1889\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1890\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m.handles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1891\u001b[39m f = \u001b[38;5;28mself\u001b[39m.handles.handle\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\common.py:873\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    868\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[32m    869\u001b[39m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[32m    870\u001b[39m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[32m    871\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m ioargs.encoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs.mode:\n\u001b[32m    872\u001b[39m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m873\u001b[39m         handle = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    880\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    881\u001b[39m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[32m    882\u001b[39m         handle = \u001b[38;5;28mopen\u001b[39m(handle, ioargs.mode)\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'"]}], "source": ["def load_and_preprocess_data(cte_file, graneis_file):\n", "    # Carregar os arquivos CSV\n", "    df_cte = pd.read_csv(cte_file, sep=';', decimal=',')\n", "    df_graneis = pd.read_csv(graneis_file, sep=';', decimal=',')\n", "\n", "    # Renomear colunas para facilitar a junção e análise\n", "    df_cte = df_cte.rename(columns={'UF do fim do transporte': 'UF_Destino', 'Valor CT-e': 'Valor_Frete'})\n", "    df_graneis = df_graneis.rename(columns={'Valor Total da Prestação do Serviço': 'Valor_Frete_Graneis'})\n", "\n", "    # Selecionar colunas relevantes e garantir tipos corretos\n", "    df_cte = df_cte[['<PERSON><PERSON>', '<PERSON><PERSON>_<PERSON><PERSON>', '<PERSON><PERSON>_Frete']]\n", "    df_graneis = df_graneis[['<PERSON><PERSON>', '<PERSON><PERSON>_<PERSON><PERSON>_Granei<PERSON>']]\n", "\n", "    # Converter '<PERSON><PERSON>_Frete' para numérico, tratando erros\n", "    df_cte['Valor_Frete'] = pd.to_numeric(df_cte['Valor_Frete'], errors='coerce')\n", "    df_graneis['<PERSON><PERSON>_<PERSON>ete_Graneis'] = pd.to_numeric(df_graneis['<PERSON>or_<PERSON>ete_Graneis'], errors='coerce')\n", "\n", "    # Remover linhas com valores nulos após a conversão\n", "    df_cte.dropna(subset=['Valor_Frete'], inplace=True)\n", "    df_graneis.dropna(subset=['Valor_Frete_Graneis'], inplace=True)\n", "\n", "    # Usaremos um merge outer para manter todos os registros e depois tratar os nulos resultantes\n", "    df_merged = pd.merge(df_cte, df_graneis, on='<PERSON><PERSON>', how='outer')\n", "\n", "    # Criar uma coluna de valor final do frete, priorizando 'Valor_Frete' do CTE e usando '<PERSON>or_Frete_Graneis' se o primeiro for nulo\n", "    df_merged['Valor_Final_Frete'] = df_merged['Valor_Frete_Graneis'].fillna(df_merged['Valor_Frete'])\n", "\n", "    # Remover linhas onde 'Valor_Final_Frete' ainda é nulo (ocorre se ambos forem nulos)\n", "    df_merged.dropna(subset=['Valor_Final_Frete'], inplace=True)\n", "\n", "    # Remover linhas onde 'UF_Destino' é nulo, pois é a variável independente da hipótese\n", "    df_merged.dropna(subset=['UF_Destino'], inplace=True)\n", "\n", "    return df_merged\n", "\n", "if __name__ == '__main__':\n", "    cte_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'\n", "    graneis_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\Graneis-22-23-24(in).csv'\n", "\n", "    df_final = load_and_preprocess_data(cte_file_path, graneis_file_path)\n", "    \n", "    # Salvar o dataframe processado para uso posterior\n", "    df_final.to_csv('c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_freight_data.csv', index=False)\n", "    print(df_final.head())\n", "    print(df_final.info())"]}, {"cell_type": "code", "execution_count": null, "id": "ef7141d6", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["F-statistic: 531.09\n", "P-value: 0.0\n", "O p-value é menor que 0.05, o que sugere que o UF de destino AFETA significativamente o valor final do frete.\n", "Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\n"]}], "source": ["def run_hypothesis_test(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    uf_counts = df[\"UF_Destino\"].value_counts()\n", "    # Filtrar UFs que aparecem menos de um certo número de vezes (ex: 30)\n", "    ufs_to_keep = uf_counts[uf_counts >= 30].index\n", "    df_filtered = df[df[\"UF_Destino\"].isin(ufs_to_keep)]\n", "\n", "    groups = []\n", "    for uf in df_filtered[\"UF_Destino\"].unique():\n", "        groups.append(df_filtered[df_filtered[\"UF_Destino\"] == uf][\"Valor_Final_Frete\"].values)\n", "\n", "    # Executar o teste ANOVA\n", "    f_statistic, p_value = stats.f_oneway(*groups)\n", "    f_statistic = round(f_statistic, 2)\n", "\n", "    print(f\"F-statistic: {f_statistic}\")\n", "    print(f\"P-value: {p_value}\")\n", "\n", "    if p_value < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o UF de destino AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o UF de destino NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_freight_data.csv'\n", "    run_hypothesis_test(processed_data_path)"]}, {"cell_type": "code", "execution_count": null, "id": "642202b0", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2500x500 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def create_freight_plot(data_file, output_image_path):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Remover UFs com poucos dados para evitar distorções no gráfico\n", "    uf_counts = df[\"UF_Destino\"].value_counts()\n", "    ufs_to_keep = uf_counts[uf_counts >= 30].index\n", "    df_filtered = df[df[\"UF_Destino\"].isin(ufs_to_keep)]\n", "\n", "    # Histograma separado por UF (subplots)\n", "    g = sns.displot(\n", "        data=df_filtered,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"UF_Destino\",\n", "        col_wrap=5,            # quantos gráficos por linha\n", "        bins=50,\n", "        facet_kws={'sharex': False, 'sharey': False},  # eixos independentes\n", "        color=\"skyblue\"\n", "    )\n", "\n", "    g.set_titles(\"UF: {col_name}\")\n", "    g.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.9)\n", "    g.fig.suptitle(\"Distribuição do Valor Final do Frete por UF de Destino\", fontsize=16)\n", "\n", "    plt.savefig(output_image_path)\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_freight_data.csv'\n", "    output_plot_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\tipo_de_uf.png'\n", "    create_freight_plot(processed_data_path, output_plot_path)"]}, {"cell_type": "markdown", "id": "ea476179", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "```<PERSON><PERSON> forma, é possível observar, de maneira clara e objetiva, que os diferentes estados de destino exercem influência significativa sobre o valor da prestação de serviço. O gráfico evidencia que há variação considerável entre os valores médios de frete por UF, mostrando que algumas unidades federativas apresentam custos consistentemente mais altos ou mais baixos. Essa diferença sugere que fatores regionais — como logística, distância e demanda local — podem impactar diretamente no valor do frete, confirmando a importância de considerar a UF de fim do transporte em análises e tomadas de decisão relacionadas ao cálculo e otimização de custos logísticos.```"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}