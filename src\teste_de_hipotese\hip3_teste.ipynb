{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a71dfde7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": null, "id": "be4347de", "metadata": {}, "outputs": [], "source": ["print(\"Hipótese 3: A sazonalidade durante o ano impacta os valores de frete?\")"]}, {"cell_type": "code", "execution_count": null, "id": "4513fe35", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados de sazonalidade processados e salvos em c:\\Users\\<USER>\\OneDrive\\Documentos\\GitHub\\2025-2A-T19-IN03-G05\\notebooks\\Dados\\processed_seasonality_data.csv\n", "  Data_Emissao  Mes  Trimestre  Valor_Final_Frete\n", "0   2022-05-16    5          2              854.0\n", "1   2022-05-23    5          2              122.0\n", "2   2022-10-17   10          4               82.0\n", "3   2022-11-21   11          4               78.0\n", "4   2023-03-04    3          1              749.0\n", "<class 'pandas.core.frame.DataFrame'>\n", "RangeIndex: 31530 entries, 0 to 31529\n", "Data columns (total 7 columns):\n", " #   Column               Non-Null Count  Dtype         \n", "---  ------               --------------  -----         \n", " 0   Chave de Acesso      31530 non-null  float64       \n", " 1   Data_Emissao         31530 non-null  datetime64[ns]\n", " 2   Valor_Frete          31530 non-null  float64       \n", " 3   Valor_Frete_Graneis  29977 non-null  float64       \n", " 4   Valor_Final_Frete    31530 non-null  float64       \n", " 5   Mes                  31530 non-null  int32         \n", " 6   Trimestre            31530 non-null  int32         \n", "dtypes: datetime64[ns](1), float64(4), int32(2)\n", "memory usage: 1.4 MB\n", "None\n"]}], "source": ["def load_and_preprocess_seasonality_data(cte_file, graneis_file):\n", "    # Carregar os arquivos CSV\n", "    df_cte = pd.read_csv(cte_file, sep=\";\", decimal=\",\", on_bad_lines=\"skip\")\n", "    df_graneis = pd.read_csv(graneis_file, sep=\";\", decimal=\",\", on_bad_lines=\"skip\")\n", "\n", "    # Renomear colunas\n", "    df_cte = df_cte.rename(columns={\n", "        'Valor CT-e': '<PERSON>or_Frete',\n", "        'Data da Emissão': 'Data_Emissao'\n", "    })\n", "    df_graneis = df_graneis.rename(columns={\n", "        'Valor Total da Prestação do Serviço': '<PERSON>or_Frete_Graneis'\n", "    })\n", "\n", "    # Selecionar colunas relevantes\n", "    df_cte = df_cte[[\"<PERSON><PERSON>\", \"Data_Emissao\", \"Valor_Frete\"]]\n", "    df_graneis = df_graneis[[\"<PERSON><PERSON>\", \"<PERSON>or_Frete_Graneis\"]]\n", "\n", "    # Converter valores de frete para numérico\n", "    df_cte[\"Valor_Frete\"] = pd.to_numeric(df_cte[\"Valor_Frete\"], errors=\"coerce\")\n", "    df_graneis[\"<PERSON>or_Frete_Graneis\"] = pd.to_numeric(df_graneis[\"Valor_Frete_Graneis\"], errors=\"coerce\")\n", "\n", "    # Remover nulos nos valores de frete\n", "    df_cte.dropna(subset=[\"Valor_Frete\"], inplace=True)\n", "    df_graneis.dropna(subset=[\"Valor_Frete_Graneis\"], inplace=True)\n", "\n", "    # Juntar os dataframes\n", "    df_merged = pd.merge(df_cte, df_graneis, on=\"Chave de Acesso\", how=\"outer\")\n", "\n", "    # Unificar colunas de valor\n", "    df_merged[\"Valor_Final_Frete\"] = df_merged[\"Valor_Frete\"].fillna(df_merged[\"Valor_Frete_Graneis\"])\n", "\n", "    # Remover linhas onde o valor final do frete ou a data são nulos\n", "    df_merged.dropna(subset=[\"Valor_Final_Frete\", \"Data_Emissao\"], inplace=True)\n", "\n", "    # Converter 'Data_Emissao' para datetime\n", "    df_merged[\"Data_Emissao\"] = pd.to_datetime(df_merged[\"Data_Emissao\"], format= '%d/%m/%Y', errors=\"coerce\")\n", "    df_merged.dropna(subset=[\"Data_Emissao\"], inplace=True)\n", "\n", "    # Extrair o mês e o trimestre\n", "    df_merged[\"Mes\"] = df_merged[\"Data_Emissao\"].dt.month\n", "    df_merged[\"Trimestre\"] = df_merged[\"Data_Emissao\"].dt.quarter\n", "\n", "    return df_merged\n", "\n", "if __name__ == '__main__':\n", "    cte_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'\n", "    graneis_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\Graneis-22-23-24(in).csv'\n", "\n", "    df_final = load_and_preprocess_seasonality_data(cte_file_path, graneis_file_path)\n", "    \n", "    # Salvar o dataframe processado\n", "    df_final.to_csv('c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv', index=False)\n", "    print(df_final[[\"<PERSON>_Emissao\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Valor_Final_Frete\"]].head())\n", "    print(df_final.info())"]}, {"cell_type": "code", "execution_count": 18, "id": "3b0313b4", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "--- <PERSON>e de Sazonalidade por Mês ---\n", "F-statistic (Mês): 410.72\n", "P-value (Mês): 0.0\n", "O p-value é menor que 0.05, o que sugere que o Mês AFETA significativamente o valor final do frete.\n", "Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\n", "\n", "--- Teste de Sazonalidade por Trimestre ---\n", "F-statistic (Trimestre): 589.3\n", "P-value (Trimestre): 0.0\n", "O p-value é menor que 0.05, o que sugere que o Trimestre AFETA significativamente o valor final do frete.\n", "Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\n"]}], "source": ["def run_seasonality_hypothesis_test(data_file):\n", "    df = pd.read_csv(data_file)\n", "    \n", "    # Preparar os dados para ANOVA por Mês\n", "    groups_month = []\n", "    for mes in sorted(df[\"Me<PERSON>\"].unique()):\n", "        groups_month.append(df[df[\"Mes\"] == mes][\"Valor_Final_Frete\"].values)\n", "\n", "    f_statistic_month, p_value_month = stats.f_oneway(*groups_month)\n", "    f_statistic_month = round(f_statistic_month, 2)\n", "\n", "    print(\"\\n--- Teste de Sazonalidade por Mês ---\")\n", "    print(f\"F-statistic (<PERSON>ê<PERSON>): {f_statistic_month}\")\n", "    print(f\"P-value (<PERSON><PERSON><PERSON>): {p_value_month}\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o Mês AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o Mês NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "    # Preparar os dados para ANOVA por Trimestre\n", "    groups_quarter = []\n", "    for trimestre in sorted(df[\"Trimestre\"].unique()):\n", "        groups_quarter.append(df[df[\"Trimestre\"] == trimestre][\"Valor_Final_Frete\"].values)\n", "\n", "    f_statistic_quarter, p_value_quarter = stats.f_oneway(*groups_quarter)\n", "    f_statistic_quarter = round(f_statistic_quarter, 2)\n", "\n", "    print(\"\\n--- Teste de Sazonalidade por Trimestre ---\")\n", "    print(f\"F-statistic (Trimestre): {f_statistic_quarter}\")\n", "    print(f\"P-value (Trimestre): {p_value_quarter}\")\n", "\n", "    if p_value_quarter < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o Trimestre AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o Trimestre NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv'\n", "    run_seasonality_hypothesis_test(processed_data_path)"]}, {"cell_type": "code", "execution_count": 19, "id": "83bd66fe", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 2000x1500 with 12 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x1000 with 4 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def create_seasonality_plots(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Histograma por Mês\n", "    g_month = sns.displot(\n", "        data=df,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"Me<PERSON>\",\n", "        col_wrap=4,          # 4 gráficos por linha\n", "        bins=40,\n", "        facet_kws={'sharex': False, 'sharey': False},\n", "        color=\"skyblue\"\n", "    )\n", "    g_month.set_titles(\"Mês: {col_name}\")\n", "    g_month.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.9)\n", "    g_month.fig.suptitle(\"Distribuição do Valor Final do Frete por Mês\", fontsize=16)\n", "    g_month.savefig(\"c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\sazonalidade_por_mes.png\")\n", "\n", "    # Histograma por Trimestre\n", "    g_quarter = sns.displot(\n", "        data=df,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"Trimestre\",\n", "        col_wrap=2,          # 2 gráficos por linha\n", "        bins=40,\n", "        facet_kws={'sharex': False, 'sharey': False},\n", "        color=\"lightcoral\"\n", "    )\n", "    g_quarter.set_titles(\"Trimestre: {col_name}\")\n", "    g_quarter.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.85)\n", "    g_quarter.fig.suptitle(\"Distribuição do Valor Final do Frete por Trimestre\", fontsize=16)\n", "    g_quarter.savefig(\"c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\sazonalidade_por_trimestre.png\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv'\n", "    create_seasonality_plots(processed_data_path)"]}, {"cell_type": "markdown", "id": "eff776fa", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "```A análise mostra que os valores de frete variam conforme o mês e o trimestre, indicando a existência de padrões sazonais. Isso reforça que a época do ano pode influenciar significativamente o custo do transporte.```"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.6"}}, "nbformat": 4, "nbformat_minor": 5}