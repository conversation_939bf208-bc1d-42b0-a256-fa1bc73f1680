{"cells": [{"cell_type": "code", "execution_count": 34, "id": "a8a62d3b", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 35, "id": "c6faaf92", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hipótese 1: A distribuição do valor do frete é diferente entre os produtos GESSO e CALCÁRIO?\n"]}], "source": ["print(\"Hipótese 1: A distribuição do valor do frete é diferente entre os produtos GESSO e CALCÁRIO?\")"]}, {"cell_type": "code", "execution_count": 36, "id": "60835832", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["       Produto  <PERSON>or_Final_Frete\n", "42       GESSO              915.0\n", "1730  CALCARIO              520.0\n", "1731  CALCARIO              520.0\n", "1732  CALCARIO              520.0\n", "1733  CALCARIO              520.0\n", "<class 'pandas.core.frame.DataFrame'>\n", "Index: 16565 entries, 42 to 31361\n", "Data columns (total 7 columns):\n", " #   Column               Non-Null Count  Dtype  \n", "---  ------               --------------  -----  \n", " 0   <PERSON>ve de Acesso      16565 non-null  float64\n", " 1   Produto_CTE          16375 non-null  object \n", " 2   Valor_Frete          16565 non-null  float64\n", " 3   Produto_Graneis      16565 non-null  object \n", " 4   Valor_Frete_Graneis  16565 non-null  float64\n", " 5   Valor_Final_Frete    16565 non-null  float64\n", " 6   Produto              16565 non-null  object \n", "dtypes: float64(4), object(3)\n", "memory usage: 1.0+ MB\n", "None\n"]}], "source": ["def load_and_preprocess_product_data(cte_file, graneis_file):\n", "    # Carregar os arquivos CSV\n", "    df_cte = pd.read_csv(cte_file, sep=\";\", decimal=\",\", on_bad_lines='skip')\n", "    df_graneis = pd.read_csv(graneis_file, sep=\";\", decimal=\",\", on_bad_lines='skip')\n", "\n", "    # Renomear colunas\n", "    df_cte = df_cte.rename(columns={'Valor CT-e': 'Valor_Frete', 'Produto': 'Produto_CTE'})\n", "    df_graneis = df_graneis.rename(columns={'Valor Total da Prestação do Serviço': 'Valor_Frete_Graneis', 'Produto': '<PERSON><PERSON><PERSON>_Graneis'})\n", "\n", "    # Selecionar colunas relevantes\n", "    df_cte = df_cte[['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>_CT<PERSON>', '<PERSON>or_Frete']]\n", "    df_graneis = df_graneis[['<PERSON><PERSON>', '<PERSON><PERSON><PERSON>_Graneis', '<PERSON><PERSON>_Fr<PERSON>_Graneis']]\n", "\n", "    # Converter valores de frete para numérico\n", "    df_cte['Valor_Frete'] = pd.to_numeric(df_cte['Valor_Frete'], errors='coerce')\n", "    df_graneis['<PERSON><PERSON>_<PERSON>ete_Graneis'] = pd.to_numeric(df_graneis['<PERSON>or_<PERSON>ete_Graneis'], errors='coerce')\n", "\n", "    # Remover nulos nos valores de frete\n", "    df_cte.dropna(subset=['Valor_Frete'], inplace=True)\n", "    df_graneis.dropna(subset=['Valor_Frete_Graneis'], inplace=True)\n", "\n", "    # Juntar os dataframes\n", "    df_merged = pd.merge(df_cte, df_graneis, on='<PERSON><PERSON>', how='outer')\n", "\n", "    # Unificar colunas de valor e produto\n", "    df_merged['Valor_Final_Frete'] = df_merged['Valor_Frete'].fillna(df_merged['Valor_Frete_Graneis'])\n", "    df_merged['Produto'] = df_merged['Produto_CTE'].fillna(df_merged['Produto_Graneis'])\n", "\n", "    # Remover linhas onde o valor final do frete ou o produto são nulos\n", "    df_merged.dropna(subset=['Valor_Final_Frete', 'Produto'], inplace=True)\n", "\n", "    # Filtrar produtos para 'GESSO' ou 'CALCARIO'\n", "    df_merged['Produto'] = df_merged['Produto'].str.strip().str.upper()\n", "    df_merged = df_merged[df_merged['Produto'].str.contains('GESSO|CALCARIO', na=False)]\n", "\n", "    return df_merged\n", "\n", "if __name__ == '__main__':\n", "    cte_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'\n", "    graneis_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\Graneis-22-23-24(in).csv'\n", "\n", "    df_final = load_and_preprocess_product_data(cte_file_path, graneis_file_path)\n", "    \n", "    # Salvar o dataframe processado\n", "    df_final.to_csv('c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_product_freight_data.csv', index=False)\n", "    print(df_final[['Produto', 'Valor_Final_Frete']].head())\n", "    print(df_final.info())"]}, {"cell_type": "code", "execution_count": 37, "id": "a4d80795", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["F-statistic: 422.72\n", "P-value: 0.0\n", "O p-value é menor que 0.05, o que sugere que o tipo de produto AFETA significativamente o valor final do frete.\n", "Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\n"]}], "source": ["def run_hypothesis_test_product(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Limpar e padronizar os nomes dos produtos\n", "    df[\"Produto\"] = df[\"Produto\"].str.strip().str.upper()\n", "\n", "    # Remover produtos com poucos dados para evitar distorções no teste\n", "    product_counts = df[\"Produto\"].value_counts()\n", "    products_to_keep = product_counts[product_counts >= 30].index\n", "    df_filtered = df[df[\"Produto\"].isin(products_to_keep)]\n", "\n", "    groups = []\n", "    for product in df_filtered[\"Produto\"].unique():\n", "        groups.append(df_filtered[df_filtered[\"Produto\"] == product][\"Valor_Final_Frete\"].values)\n", "\n", "    f_statistic, p_value = stats.f_oneway(*groups)\n", "    f_statistic = round(f_statistic, 2)\n", "    p_value = round(p_value, 2)\n", "\n", "    print(f\"F-statistic: {f_statistic}\")\n", "    print(f\"P-value: {p_value}\")\n", "\n", "    if p_value < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o tipo de produto AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o tipo de produto NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_product_freight_data.csv'\n", "    run_hypothesis_test_product(processed_data_path)"]}, {"cell_type": "code", "execution_count": 38, "id": "27d26689", "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["C:\\Users\\<USER>\\AppData\\Local\\Temp\\ipykernel_9288\\3946517414.py:20: User<PERSON>arning: Ignoring `palette` because no `hue` variable has been assigned.\n", "  g = sns.displot(\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["def create_product_freight_plot(data_file, output_image_path):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Limpar e padronizar os nomes dos produtos\n", "    df[\"Produto\"] = df[\"Produto\"].str.strip().str.upper()\n", "\n", "    # Mapear variações de CALCARIO para um único termo\n", "    df[\"Produto\"] = df[\"Produto\"].replace(to_replace=r\".*CALCARIO.*\", value=\"CALCARIO\", regex=True)\n", "\n", "    # Filtrar produtos para 'GESSO' ou 'CALCARIO'\n", "    df_filtered = df[df[\"Produto\"].str.contains(\"GESSO|CALCARIO\", na=False)]\n", "\n", "    # Definir cores\n", "    product_colors = {\n", "        \"GESSO\": \"skyblue\",\n", "        \"CALCARIO\": \"lightcoral\"\n", "    }\n", "\n", "    # Criar histograma separado por produto (subplots)\n", "    g = sns.displot(\n", "        data=df_filtered,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"Produto\",\n", "        col_wrap=2,            # dois gráficos lado a lado\n", "        bins=50,\n", "        facet_kws={'sharex': False, 'sharey': False},\n", "        palette=product_colors,\n", "        color=None\n", "    )\n", "\n", "    g.set_titles(\"{col_name}\")\n", "    g.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.85)\n", "    g.fig.suptitle(\"Distribuição do Valor Final do Frete por Tipo de Produto (Gesso e Calcário)\", fontsize=16)\n", "    \n", "    plt.savefig(output_image_path)\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_product_freight_data.csv'\n", "    output_plot_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\tipo_de_produto.png'\n", "    create_product_freight_plot(processed_data_path, output_plot_path)"]}, {"cell_type": "markdown", "id": "4bf9523e", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON> \n", "\n", "```Temos como evidência que o gráfico mostra que a distribuição dos valores de frete varia entre gesso e calcário, indicando que o tipo de produto transportado influencia diretamente nos custos logísticos. Enquanto um produto apresenta maior concentração em faixas de valores mais baixos, o outro tende a ter uma dispersão mais ampla, reforçando a hipótese de que há diferenças significativas no valor do frete em função do tipo de carga.```"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.6"}}, "nbformat": 4, "nbformat_minor": 5}