import pandas as pd
import numpy as np
import matplotlib.pyplot as plt

df = pd.read_csv('notebooks\Dados\Graneis-22-23-24(in).csv', sep=';')

# Converter colunas para formato numérico (tratar formato brasileiro)
df['Valor Total da Prestação do Serviço'] = (df['Valor Total da Prestação do Serviço']
                                             .astype(str)
                                             .str.replace('.', '', regex=False)
                                             .str.replace(',', '.', regex=False))
df['Valor do ICMS'] = (df['Valor do ICMS']
                       .astype(str)
                       .str.replace('.', '', regex=False)
                       .str.replace(',', '.', regex=False))

df['Valor Total da Prestação do Serviço'] = pd.to_numeric(df['Valor Total da Prestação do Serviço'], errors='coerce')
df['Valor do ICMS'] = pd.to_numeric(df['Valor do ICMS'], errors='coerce')

valor_sem_imposto = df['Valor Total da Prestação do Serviço'] - df['Valor do ICMS']
print(valor_sem_imposto.head())