{"cells": [{"cell_type": "code", "execution_count": 1, "id": "def85b24", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 2, "id": "3f16e010", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Resultados dos Testes de Normalidade:\n", "| Variável            | Teste de Normalidade   |   Estatística |   P-valor | Normalidade (p > 0.05)   |\n", "|:--------------------|:-----------------------|--------------:|----------:|:-------------------------|\n", "| Valor_Final_Frete   | <PERSON><PERSON><PERSON><PERSON><PERSON>           |        0.7871 |         0 | <PERSON><PERSON>                      |\n", "| Valor_Frete         | <PERSON><PERSON><PERSON>           |        0.7871 |         0 | <PERSON><PERSON>                      |\n", "| <PERSON><PERSON>_<PERSON><PERSON>_Granei<PERSON> | <PERSON><PERSON><PERSON><PERSON><PERSON>           |        0.7794 |         0 | <PERSON><PERSON>                      |\n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Python313\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py:579: UserWarning: scipy.stats.shapiro: For N > 5000, computed p-value may not be accurate. Current N is 16565.\n", "  res = hypotest_fun_out(*samples, **kwds)\n"]}], "source": ["def run_normality_tests(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Se<PERSON><PERSON>ar as variáveis quantitativas relevantes\n", "    variables = [\"Valor_Final_Frete\", \"Valor_Frete\", \"Valor_Frete_Graneis\"]\n", "    \n", "    results = []\n", "    for var in variables:\n", "        # Remover valores nulos para o teste de normalidade\n", "        data = df[var].dropna()\n", "\n", "        # Realizar o teste de <PERSON>-Wilk\n", "        if len(data) > 1:\n", "            statistic, p_value = stats.shapiro(data)\n", "            test_name = \"<PERSON><PERSON><PERSON><PERSON><PERSON>\"\n", "        else:\n", "            statistic, p_value = None, None\n", "            test_name = \"N/A (dados insuficientes)\"\n", "\n", "        results.append({\n", "            \"Variável\": var,\n", "            \"Teste de Normalidade\": test_name,\n", "            \"Estatística\": f\"{statistic:.4f}\" if statistic is not None else \"N/A\",\n", "            \"P-valor\": f\"{p_value:.4f}\" if p_value is not None else \"N/A\",\n", "            \"Normalidade (p > 0.05)\": \"Sim\" if (p_value is not None and p_value > 0.05) else \"Não\"\n", "        })\n", "\n", "    results_df = pd.DataFrame(results)\n", "    print(\"\\nResultados dos Testes de Normalidade:\")\n", "    print(results_df.to_markdown(index=False))\n", "    \n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_product_freight_data.csv'\n", "    run_normality_tests(processed_data_path)"]}, {"cell_type": "code", "execution_count": 3, "id": "6e8f54fd", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "Comparação de Média e Mediana:\n", "| Variável            |   Média |   Mediana |\n", "|:--------------------|--------:|----------:|\n", "| Valor_Final_Frete   |  746.18 |       861 |\n", "| Valor_Frete         |  746.18 |       861 |\n", "| Valor_Frete_Graneis |  754.2  |       861 |\n"]}], "source": ["def compare_mean_median(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    variables = [\"Valor_Final_Frete\", \"Valor_Frete\", \"Valor_Frete_Graneis\"]\n", "    \n", "    comparison_results = []\n", "    for var in variables:\n", "        data = df[var].dropna()\n", "        if not data.empty:\n", "            mean_val = data.mean()\n", "            median_val = data.median()\n", "            comparison_results.append({\n", "                \"Variável\": var,\n", "                \"Média\": f\"{mean_val:.2f}\",\n", "                \"Mediana\": f\"{median_val:.2f}\"\n", "            })\n", "        else:\n", "            comparison_results.append({\n", "                \"Variável\": var,\n", "                \"Média\": \"N/A\",\n", "                \"Mediana\": \"N/A\"\n", "            })\n", "\n", "    results_df = pd.DataFrame(comparison_results)\n", "    print(\"\\nComparação de Média e Mediana:\")\n", "    print(results_df.to_markdown(index=False))\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_product_freight_data.csv'\n", "    compare_mean_median(processed_data_path)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.6"}}, "nbformat": 4, "nbformat_minor": 5}