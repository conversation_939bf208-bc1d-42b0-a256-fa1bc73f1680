{"cells": [{"cell_type": "code", "execution_count": 4, "id": "d4b15a51", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import matplotlib.pyplot as plt\n", "from scipy.stats import shapiro"]}, {"cell_type": "code", "execution_count": 5, "id": "def85b24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["=== Média e Mediana ===\n", "\n", "                                        <PERSON><PERSON><PERSON>\n", "Valor Total da Prestação do Serviço   5459.75   5183.06\n", "Valor total da carga                  6107.44   4100.18\n", "Quantidade de Carga 01               40464.25  45525.00\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "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", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAAsAAAAHWCAYAAAB5SD/0AAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjUsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvWftoOwAAAAlwSFlzAAAPYQAAD2EBqD+naQAATkFJREFUeJzt3Qd8FNX6//EnEAgkEGpoAgGl92IBsdAEFb1SVK6CIkXFiwVRUJRLs4AoIAqICgJeVAQFlaI0AQtNEKQpRUNAablSEgJpZP6v5/z+s3d3U0hZspvM5/16LWFnJ2dnZ0u+e+acZ4Isy7IEAAAAcIhC/t4AAAAAIC8RgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgIF8rkaNGvLQQw/5ezNQAAUFBcno0aMvuZ6uo+vmx9c17x/AmQjAQACZM2eOCRJbt25N9/a2bdtKo0aNcn0/y5cvz1KwQc4tXbpUbr31VilXrpwUK1ZM6tSpI0OHDpVTp05JIOG1cHksXrxYbrvtNilfvrwULVpUqlSpIvfee698++23kl/99ddf5jGULl1awsPD5a677pI//vgjzXrvvPOO3HPPPVK9enXzecYXDASiYH9vAIDc2bdvnxQqVCjboWfatGkEn8vk2WeflYkTJ0rTpk3lueeek7Jly8rPP/8sb7/9tnz66aeyZs0aqV27tgSCzF4LFy5ckOBg/kxkh2VZ0q9fP/Nltnnz5jJkyBCpVKmSHDt2zITiDh06yI8//ijXX3+95Cfnzp2Tdu3aydmzZ+WFF16QIkWKyOTJk+Xmm2+WHTt2mC96ttdee03i4uLk2muvNY8bCER8sgH5XEhIiOQ38fHxEhYWJgXRJ598YsJvz5495aOPPpLChQu7btOeMA0R2jumvfyBHi615xrZo8+9ht/BgwfLpEmTPIaGvPjii/Kf//zHJ897QkKC6VnO7pffnJo+fbocOHBAtmzZItdcc41Zpj3cekRKH/Orr77qWnf9+vWu3t8SJUrkyfYB2cUQCCCf8x7DmJycLGPGjDE9jBpgtGfmhhtukFWrVpnbdV3t8VP6B8q+uIfTZ555RqpVq2bCdd26deWNN94wPVvevYNPPvmkOcRbsmRJ+cc//mEOkXqPG7XHh+7du1fuv/9+KVOmjNketXPnTrM9V155pdlW7SnT3rO///7b477sNvbv3y+9e/eWUqVKSUREhPz73/8223XkyBFzOFYPy2ob+gfZXVJSkowcOVJatmxpflfD94033ihr164VX9N9r4/xvffe8wi/SnvEtEf4l19+kUWLFl1yHKoOedFLdh/HoUOHzP7S502346qrrjLPpQaXn376ybXepV4L6Y0B/uGHH0w7+nxpu++++266+2H27NnSvn17qVChgrnvBg0amEPj3vT5e/nll6Vq1aoSGhpqviDs2bMn3TbPnDljgqX92qxVq5bpbUxNTU13/by+H31PjBs3TurVq2f2fXrjoh944AHzOlA6HEaPFjRu3NgERX39aqjU14e7devWmbbmz58vI0aMkCuuuMI8htjYWHP7woULzf7V50QDqfY063Orryt3uk3a86yfCcWLFzevo88++0yyQtfT590Ov0ofp/ZoL1iwwGPdyMhIn48JB3wtsLsfAIfSw4z//e9/0yzXcHspGlj0j/CAAQPMH1r9I6m9jXoI/pZbbpFHH31Ujh49agKx9kZ5hwQNshqo+vfvL82aNZMVK1aYsasabvWQp03/wOofPv2D3qpVK9Pr06VLlwy3S3s9NZRrT5EdpnUbdAxh3759TXDVQKKBTX9u2rQpzR9R7VWtX7++jB8/XpYtW2YCjQ4v0BCmYUtDiva6aqjQP9Q33XST+T3dBzNnzpT77rtPHn74YXN4dtasWdK5c2fTo6WP0xe0h0yHpOi+0TCTngcffFBGjRolS5YsMeMpsyO7j+Pjjz826+hzrvtywoQJ0r17d7PP9RB2Zq+F9OzatUs6depkvnzo6ywlJcU8looVK6ZZV8Nuw4YNzetJezz18f7rX/8yIXLQoEGu9TTQ6/N4++23m4u+TvU+NOy7O3/+vDncrq9D3W7tYdywYYMMHz7cHGZ/8803M932vLgf/XKgoVbDs/eXn/To8/DFF1+Y90bNmjXlxIkT5rWs969fGHXcsLuXXnrJ9Prq6zsxMdH8X98H+r7QEK3v+9OnT5v3roZkb1OmTDHPR69evczj1kCt963j1TN77+pzpl9W9cupN/2MWblypXmd6RdhIN+wAASM2bNnazLM9NKwYUOP34mMjLT69Onjut60aVOrS5cumd7PoEGDTFvevvjiC7P85Zdf9lh+9913W0FBQdbBgwfN9W3btpn1Bg8e7LHeQw89ZJaPGjXKtUz/r8vuu+++NPd3/vz5NMs++eQTs/53332Xpo1HHnnEtSwlJcWqWrWq2a7x48e7lp8+fdoqXry4xz7RdRMTEz3uR9erWLGi1a9fP8tX7P03efLkTNcLDw+3WrRokeFzaLv55pvNJbuPIyoqymxHuXLlrFOnTrmWf/nll2b5kiVLLvlaUN7PZdeuXa1ixYpZ0dHRrmV79+61ChcunKaN9J7bzp07W1deeaXr+smTJ62iRYua12tqaqpr+QsvvGDac98nL730khUWFmbt37/fo83nn3/e3P/hw4fTfQx5eT9Tpkwx7S1evNjKioSEBOvixYsey/S5CwkJscaOHetatnbtWtOu7jvv/dq4cWPzPoiLi3MtW7dunVlfX1fuvH83KSnJatSokdW+fftMtzMmJsa0575NtmnTppnbfvvtt3R/V/dleq9twN8YAgEEID0srb1y3pcmTZpc8nd1hrb2oGpvZE4mRGnPlQ5tcKdDIjQPff311+b6N998Y35qj567J554IsO2Bw4cmGaZHoZ1H9Oovd7am6y0h86b9mrbdDuvvvpqs13a4+X++HXYhvvsdF1Xe8vs3iztpdPeS/399O4np7QXTF2qJ0xvt9fNjuw+Du0Z1OEYNh0uodKbuX8pFy9eNEcDunbtanpFbdojrz3QmT239hEN7dnU+9bravXq1aYnUl837r392oPqTQ/z6/br49G27EvHjh3Ntn333XcZbnte3Y89JCGrPaE6vMIew6tt69AfHQqhr9/0ns8+ffp47FftvddeeT2q4D7WVvez9gh7c/9d7SnW50Ef66XeAzq0w97ejMaJ2+sA+QVDIIAApIcVNdR4s/8oZ2bs2LFmPKyW3dLxgFqKS4cpZCU8R0dHm8Ou3n/ANeTYt9s/9Q+3HrZ1p2MlM+K9rtIAp2Nm9VDsyZMnPW6zQ5I79+CldBys/gHWccjey73HEc+dO9eMDf7tt988hpKkt13e2+H+x10DqA67SI+93y4VbvV27/GZWZWdx+G9v+wwrOEnu2JiYsx+SK96hQY2/fLkTisd6PCIjRs3mmEF3vtUnyP79eTdpg6xcA/uSr/Q6WF4vS093q8fd3l1P/awl6x+udEvMTosQSeYRUVFmRBsc6+qkNFzbD+u9N53usw72OpQBx0GolUbdAiF7VLjde3g7P477l9c3dcB8gsCMFDA6LjX33//Xb788kszNk/HjOrY3RkzZnj0oOa19P5A6hhYHV+pY4x1/Kr2Ymko0NCe3oSj9MZVZjTW0n3S3rx588y4XO291PvSiVn6ezpmUvdVZp566ikTOt1713RSUnp0IpLSAJURDS3aU6gT/y4VQDQQuT++7D6OrOyby0G3RSdH6SQprYSgk8n0i4OGZH0tZmXSmjf9HR3DPmzYsHRv1y98vpCb+9HHq7RXVp+jS9Hx8DqRU8fW6vhe/WKlXyy1Zzq9fZSbkPn999+b8b/6+aCBu3LlymYcuE5W1LHimdHt0t7f9Eqa2cu8xysDgY4ADBRA+gdLJ5bpRet36h89nbRkB+CMApfO3tbDxd4TWrS30b7d/ql/oLXXyr1X7eDBg1neRu2F1Hq42gOsE5RsORm6kZUZ7Bo4tfKC+2PXHspL0SCklSds3j2G7nRfaG+oTmzSnr30DoV/+OGH5qdOPnJvUysPpBeW3YNybh5HRrI6W197RDWApff86MQ/dzrhTXsLv/rqK49eaO9qFfbrSdt0f5za2+zdS60VJ/S1rEMRsiuv7kerm+hzqaXwtFbupSbC6fOp1Sh0IqM7fS14H9XI7HGl977zXvb555+boyU6jMV9KIMG4EvRUK5DKtI7Qc/mzZvNPmUCHPIbxgADBYz3oX/tVdXDoe6HL+0avN6hS2fHa6/j1KlTPZZrr50GJS3RpOwxn9qT5E5P9JBVdjjw7o281Gz+nEjvvvQPtx6evxTt1dUwZF+0dFRmNIxqqNIxz+6HtNW2bdtMpQo9QYK9L+3QpVUv3CsS6OFqLe/mq8eRkYxeC970vvV513B/+PBh1/Jff/3VhKpLbacOe/AOW7o/tRdSXzfu66b3GtCjBfo4ve/L3nYdC52RvLofLU2mZe50n+jP9HratRdfK3bY+8l7HR2DrBUoskJ7XXWYk36p0tBu04os2gvtTu9L38Pur0ktl6fPZ1bcfffdpoSeewjWLz56Zjv3L3NAfkEPMFDAaGDT2rEa1LQnWP9gaU/T448/7lrHDnE62U1Djf5x/Oc//yl33nmn6ZHSgv36x1HPZKbDKHQ4hR6W1aBm/36PHj1MgNDAbZdB0zq9We1V1PGS2jOtpbl0LKuWbdL70l5lX7vjjjtMr2m3bt1MuSe9Dx0SovvKPTj4gpYo032uh/61lJWWnNJeQR2P+cEHH5ieVH0+3E+GoD3zukyHfmgA0yEEGpTs/X05H0dGr4X0aG+9ToDUiVM6AVLDoIZKLXfmPuxDy4vpkAd9PWkpMd22999/3wzZcD+MrvtCS3rpEA59bPoFbPv27WaypXcPqA750B5lXU+Hgeh2a81qDXq67/T1mlGvaV7dj/37OglVx2lrj7cGRy3xd/z4cRM2NfzqsB+l96Fj9vVIjdbn1fvQMn7uvdRZGUahY/7btGlj2tEvX/oFVoOx+2tCXy/6mtTXmNbj1rHMOtlWvxxnNmTHps+3Pofaju5L/UKh7WkJPJ0k630EwK5lrO9tbV/HHisdhpGV+QjAZefvMhQA0pZB++mnn9K9XUtiXaoMmpYwu/baa63SpUubcmD16tWzXnnlFVPyyL2c1hNPPGFFRESYMmLuHwVaTunpp5+2qlSpYhUpUsSqXbu29frrr3uUj1Lx8fGmhFbZsmWtEiVKmBJZ+/btM225lyWzS5hpKSVvf/75p9WtWzezraVKlbLuuece6+jRoxmWUvNuQx+3llm61H7SbX/11VfNvtISU82bN7eWLl1qft+7VJSvfPXVV1bHjh3NY3MvYXf27Nl01584caJ1xRVXmO1r06aNtXXr1jRl0LL6OOwyaPq8efPet5m9FrzXVevXr7datmxpyoppWa4ZM2a4nh/vx9+kSRNTNq1GjRrWa6+9Zn3wwQdmPd0+m5YBGzNmjFW5cmXzem3btq21e/fudEvD6Wtz+PDhVq1atcz9ly9f3rr++uutN954w+P1nZ68uh/bZ599ZnXq1Mm8P4KDg8399uzZ05Qocy+D9swzz7i2SZ/3jRs3pnne7TJoCxcuTPe+5s+fb97n+prQsma673v06GGWuZs1a5Z5P+t6ept+3qT33GXkyJEjpiSilvHT9/wdd9xhHThwIM16uj8zKuOo9wkEgiD95/LHbABOoLPL9fC+9l5qzyfEo5dXx3pqL5o/JyPCGXRSqfZ822eABOCJMcAAciS9up86JEInzNhnYMP/6Bm+9JD3Y489lqZkGJBTOsTAe1yyVinRIQjup9EG4IkeYAA5ouNBdVKXjhnW8aw6nlIvjzzyiAl7AC4/HZOsk/y0UolOitOKLTouXOss7969O916wgAIwABySA+tagjWiV462UbLXekJN3QCnfsELwCXj1bX0C+deuIRLeumVT20BvP48ePTTKIE8D8EYAAAADgKY4ABAADgKARgAAAAOAoD9bJIT/t69OhRc7rHrJ46FAAAAHlHR/bGxcWZSaFalSgjBOAs0vBbrVo1f28GAAAALkFPJV+1atUMbycAZ5H2/No7VE/hCgAAgMASGxtrOizt3JYRAnAW2cMeNPwSgAEAAALXpYarMgkOAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKME+3sDAADID2JiYiQ2NtYnbYWHh0tERIRP2gKQfQRgAACyEH579x0gp+LO+6S9siVDZd7smYRgwE8IwAAAXIL2/Gr4jWjdQ8LKVsxVW/GnTkjMxs9NmwRgwD8IwAAAZJGG3/AKVXPdToxPtgZATjEJDgAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICj+DUAjx49WoKCgjwu9erVc92ekJAggwYNknLlykmJEiWkR48ecuLECY82Dh8+LF26dJHQ0FCpUKGCDB06VFJSUjzWWbdunbRo0UJCQkKkVq1aMmfOnDx7jAAAAAgsfu8BbtiwoRw7dsx1+eGHH1y3Pf3007JkyRJZuHChrF+/Xo4ePSrdu3d33X7x4kUTfpOSkmTDhg0yd+5cE25HjhzpWicqKsqs065dO9mxY4cMHjxYBgwYICtWrMjzxwoAAAD/C/b7BgQHS6VKldIsP3v2rMyaNUs+/vhjad++vVk2e/ZsqV+/vmzatElatWolK1eulL1798rq1aulYsWK0qxZM3nppZfkueeeM73LRYsWlRkzZkjNmjVl4sSJpg39fQ3ZkydPls6dO+f54wUAAIDDA/CBAwekSpUqUqxYMWndurWMGzdOqlevLtu2bZPk5GTp2LGja10dHqG3bdy40QRg/dm4cWMTfm0aah977DHZs2ePNG/e3Kzj3oa9jvYEZyYxMdFcbLGxseZnamqquQAAnMOyrP8bqiciQWLlqi3TRlCQaZO/J4BvZfU95dcAfN1115khC3Xr1jXDH8aMGSM33nij7N69W44fP256cEuXLu3xOxp29TalP93Dr327fVtm62igvXDhghQvXjzdbdMgrtvjLSYmxoxNBgA4R1xcnNSqGSkVwkRCi/yvcyQnSoSJBNeMNG2ePHnSZ9sIQMz7KuAD8G233eb6f5MmTUwgjoyMlAULFmQYTPPK8OHDZciQIa7rGpirVasmEREREh4e7tdtAwDkrXPnzsnBqGhJqS8SHhaSq7Zi40UORUVLyZIlzeRtAL6jIwryxRAId9rbW6dOHTl48KDccsstZnLbmTNnPHqBtQqEPWZYf27ZssWjDbtKhPs63pUj9LqG2MxCtlaM0Iu3QoUKmQsAwDnsIQs6+MEygxhyznIbUsHfE8C3svqeKhRo37B///13qVy5srRs2VKKFCkia9ascd2+b98+U/ZMxwor/blr1y6PQ0irVq0y4bZBgwauddzbsNex2wAAAICz+DUAP/vss6a82aFDh0wZs27duknhwoXlvvvuk1KlSkn//v3NMIS1a9eaSXF9+/Y1wVUnwKlOnTqZoPvAAw/IL7/8YkqbjRgxwtQOtntvBw4cKH/88YcMGzZMfvvtN5k+fboZYqEl1gAAAOA8fh0C8eeff5qw+/fff5uxtTfccIMpcab/V1qqTLuy9QQYWpFBqzdogLVpWF66dKmp+qDBOCwsTPr06SNjx451raMl0JYtW2YC75QpU6Rq1aoyc+ZMSqABAAA4lF8D8Pz58y85kHnatGnmkhGdNLd8+fJM22nbtq1s3749x9sJAACAgiOgxgADAAAAlxsBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKMH+3gAAQPbExMRIbGysT9oKDw+XiIgIn7QFAPkFARgA8ln47d13gJyKO++T9sqWDJV5s2cSggE4CgEYAPIR7fnV8BvRuoeEla2Yq7biT52QmI2fmzYJwACchAAMAPmQht/wClVz3U6MT7YGAPIXJsEBAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcJSACsDjx4+XoKAgGTx4sGtZQkKCDBo0SMqVKyclSpSQHj16yIkTJzx+7/Dhw9KlSxcJDQ2VChUqyNChQyUlJcVjnXXr1kmLFi0kJCREatWqJXPmzMmzxwUAAIDAETAB+KeffpJ3331XmjRp4rH86aefliVLlsjChQtl/fr1cvToUenevbvr9osXL5rwm5SUJBs2bJC5c+eacDty5EjXOlFRUWaddu3ayY4dO0zAHjBggKxYsSJPHyMAAAD8LyAC8Llz56RXr17y/vvvS5kyZVzLz549K7NmzZJJkyZJ+/btpWXLljJ79mwTdDdt2mTWWblypezdu1fmzZsnzZo1k9tuu01eeuklmTZtmgnFasaMGVKzZk2ZOHGi1K9fXx5//HG5++67ZfLkyX57zAAAAPCPYAkAOsRBe2g7duwoL7/8smv5tm3bJDk52Sy31atXT6pXry4bN26UVq1amZ+NGzeWihUrutbp3LmzPPbYY7Jnzx5p3ry5Wce9DXsd96EW3hITE83FFhsba36mpqaaCwD4g2VZZqhYkIgEiZWrtkwbQUGmTT7XMsd+B/KHrL6n/B6A58+fLz///LMZAuHt+PHjUrRoUSldurTHcg27epu9jnv4tW+3b8tsHQ21Fy5ckOLFi6e573HjxsmYMWPSLI+JiTHjkgHAH+Li4qRWzUipECYSWuR/X9JzokSYSHDNSNPmyZMnfbaNBRH7Hcgf9H0V8AH4yJEj8tRTT8mqVaukWLFiEkiGDx8uQ4YMcV3XsFytWjWJiIiQ8PBwv24bAOfSIWMHo6Ilpb5IeFhIrtqKjRc5FBUtJUuWNBOIkTH2O5A/ZDVP+jUA6xAH/far1RncJ7V99913MnXqVDNJTcfxnjlzxqMXWKtAVKpUyfxff27ZssWjXbtKhPs63pUj9LoG2fR6f5VWi9CLt0KFCpkLAPiDfehcD8Jb5mB6zlluh/b5XMsc+x3IH7L6nvLrO69Dhw6ya9cuU5nBvlx99dVmQpz9/yJFisiaNWtcv7Nv3z5T9qx169bmuv7UNtwPI2mPsobbBg0auNZxb8Nex24DAAAAzuHXHmA9/NOoUSOPZWFhYabmr728f//+ZihC2bJlTah94oknTHDVCXCqU6dOJug+8MADMmHCBDPed8SIEWZind2DO3DgQNOjPGzYMOnXr598++23smDBAlm2bJkfHjUAAAD8ye+T4C5FS5Vpd7aeAEOrMmj1hunTp7tuL1y4sCxdutRUfdBgrAG6T58+MnbsWNc6WgJNw67WFJ4yZYpUrVpVZs6cadoCAACAswRcANYztnkPZtaavnrJSGRkpCxfvjzTdtu2bSvbt2/32XYCAAAgf2L0PQAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcJTg3Pzy1q1bZcGCBXL48GFJSkryuG3RokW53TYAAAAgcHqA58+fL9dff738+uuvsnjxYklOTpY9e/bIt99+K6VKlfLtVgIAAAD+DsCvvvqqTJ48WZYsWSJFixaVKVOmyG+//Sb33nuvVK9e3VfbBwAAAARGAP7999+lS5cu5v8agOPj4yUoKEiefvppee+993y5jQAAAID/A3CZMmUkLi7O/P+KK66Q3bt3m/+fOXNGzp8/77stBAAAAAJhEtxNN90kq1atksaNG8s999wjTz31lBn/q8s6dOjgy20EAAAA/B+Ap06dKgkJCeb/L774ohQpUkQ2bNggPXr0kBEjRvhuCwEAAIBACMBly5Z1/b9QoULy/PPP+2qbAAAAgMAIwLGxsRIeHu76f2bs9QAAAIB8G4B14tuxY8ekQoUKUrp0aVP1wZtlWWb5xYsXfbmdAAAAQN4HYJ3kZg99WLt2rW+2AAAAAAjUAHzzzTen+38AAACgwNcBnj17tixcuDDNcl02d+7c3G4XAAAAEFgBeNy4cVK+fPk0y3V8sJ4mGQAAAChQAfjw4cNSs2bNNMsjIyPNbVnxzjvvSJMmTUzFCL20bt1avv76a9ftWmd40KBBUq5cOSlRooSpMXzixIk026GnZA4NDTXhe+jQoZKSkuKxzrp166RFixYSEhIitWrVkjlz5uT0YQMAAMCpAVjD5s6dO9Ms/+WXX0xgzYqqVavK+PHjZdu2bbJ161Zp37693HXXXbJnzx5z+9NPPy1LliwxwyrWr18vR48ele7du7t+XytNaPhNSkoyJ+HQoRcabkeOHOlaJyoqyqzTrl072bFjhwwePFgGDBggK1asyOlDBwAAgBNPhHHffffJk08+KSVLljSnRVYaUvWUyP/85z+z1Madd97pcf2VV14xvcKbNm0y4XjWrFny8ccfm2BsjzuuX7++ub1Vq1aycuVK2bt3r6xevVoqVqwozZo1k5deekmee+45GT16tBQtWlRmzJhheqonTpxo2tDf/+GHH2Ty5MnSuXPnnD58AAAAOC0Aa9A8dOiQdOjQQYKD/6+Z1NRUefDBB3M0Blh7c7WnNz4+3gyF0F7h5ORk6dixo2udevXqSfXq1WXjxo0mAOvPxo0bm/Br01D72GOPmV7k5s2bm3Xc27DX0Z7gzCQmJpqLzT7xhz5GvQCAP9i11rUKe5BYuWrLtBEUZNrkcy1z7Hcgf8jqeyrHAVh7Vz/99FMThHXYQ/HixU0Y1THA2bFr1y4TeHW8r47zXbx4sTRo0MAMV9D70BNuuNOwe/z4cfN//ekefu3b7dsyW0cD7YULF8x2ZzTJb8yYMWmWx8TEmG0FAH+Ii4uTWjUjpUKYSGiR/31Jz4kSYSLBNSNNmydPnvTZNhZE7Hcgf9D31WUNwLY6deqYS07VrVvXhN2zZ8/KZ599Jn369DFDKfxt+PDhMmTIENd1DczVqlWTiIgITvMMwG/OnTsnB6OiJaW+SHhYSK7aio0XORQVbYay6bwOZIz9DuQPxYoVu7wBWIcs6ISzNWvWmG+w3l3Oeta4rNBeXq3MoFq2bCk//fSTTJkyRXr27Gkmt505c8ajF1irQFSqVMn8X39u2bLFoz27SoT7Ot6VI/S6htiMen+VVozQi7dChQqZCwD4g33oXA/CW+Zges5Zbof2+VzLHPsdyB+y+p7K8TtPJ7vpRYNwo0aNpGnTph6XnNIgrWNvNQwXKVLEBGzbvn37TNkzHTKh9KcOoXA/hLRq1SoTbnUYhb2Oexv2OnYbAAAAcJYc9wDPnz9fFixYILfffnuuhhncdtttZmKbjtnQig9as1dLlJUqVUr69+9vhiGULVvWhNonnnjCBFedAKc6depkgu4DDzwgEyZMMON9R4wYYWoH2723AwcOlKlTp8qwYcOkX79+pmdat3vZsmU53m4AAADkX7maBGcPXcgp7bnVqhHHjh0zgVdPiqHh95ZbbjG3a6ky7crWE2Bor7BWb5g+fbrr9wsXLixLly41VR80GIeFhZkxxGPHjnWtoyXQNOxqTWEdWqHl1WbOnEkJNAAAAIfKcQB+5plnTKDU3lUdx5QTWuf3UgOZp02bZi4Z0aoTy5cvz7Sdtm3byvbt23O0jQAAAChYchyA9WQSa9euNacubtiwoRmv627RokW+2D4AAAAgMAKwVmbo1q2bb7cGAAAACNQArKclBgAAAPKbXBUgTElJkdWrV8u7777rOvPG0aNHTcFwAAAAoED0AGudXq3MEB0dLbfeequpy6sVGrRyg57V5rXXXjPXZ8yYcXm2GAAAAMirHmA96cRNN91k/q8nwbj66qvl9OnTHmdU03HB3ieeAAAAAPJdD/Bnn31m6uvOmzfPXP/+++9lw4YNph6wuxo1ashff/3l+y0FAAAA8rIHWIc+6GmP7Zq/9nVvf/75pxkKAQAAAOTrAHzvvffKf/7zH3nkkUfMdR3z++abb7pu12Csk99GjRqVq9MjAwAAAAEzCa5FixZm6IOaNGmSOZ1wgwYNJCEhQe6//345cOCAlC9fXj755JPLtb0AAABA3laBCA7+v1+pWrWq/PLLLzJ//nzZuXOn6f3t37+/9OrVy2NSHAAAAFAgToRhfjk4WHr37u27rQEAAAACNQB/+OGHmd7+4IMP5rRpAAAAIPACsNYBdpecnCznz583ZdFCQ0MJwAAAAChYp0LWE2C4X3QM8L59++SGG25gEhwAAAAKXgBOT+3atWX8+PFpeocBAACAAhmA7YlxR48e9XWzAAAAgH/HAH/11Vce1y3LkmPHjsnUqVOlTZs2vtg2AAAAIHACcNeuXT2u65ngIiIipH379jJx4kRfbBsAAAAQOAE4NTXVt1sCAAAA5McxwAAAAECB7AEeMmRIltedNGlSTu8GAAAACIwAvH37dnPRE2DUrVvXLNu/f78ULlxYWrRo4TE2GAAAAMj3AfjOO++UkiVLyty5c6VMmTJmmZ4Qo2/fvnLjjTfKM88848vtBAAAAPw7BlgrPYwbN84VfpX+/+WXX6YKBAAAAApeD3BsbKzExMSkWa7L4uLicrtdAADkmv5N0r9XuRUdHS0pySk+2SYA+TgAd+vWzQx30N7ea6+91izbvHmzDB06VLp37+7LbQQAIEfht3ffAXIq7nyu20q4cF7+/OuYVE9O9sm2AcinAXjGjBny7LPPyv33328mwpnGgoOlf//+8vrrr/tyGwEAyDbt+dXwG9G6h4SVrZirtk7+vluij3wgF1MIwICjA3BoaKhMnz7dhN3ff//dLLvqqqskLCzMl9sHAECuaPgNr1A1V22c+/u4z7YHQAE4EcaxY8fMpXbt2ib8Wpblmy0DAAAAAikA//3339KhQwepU6eO3H777SYEKx0CQQk0AAAAFLgA/PTTT0uRIkXk8OHDZjiErWfPnvLNN9/4avsAAACAwBgDvHLlSlmxYoVUreo5rkqHQmi5GAAAAKBA9QDHx8d79PzaTp06JSEhIbndLgAAACCwArCe7vjDDz90XQ8KCpLU1FSZMGGCtGvXzlfbBwAAAATGEAgNujoJbuvWrZKUlCTDhg2TPXv2mB7gH3/80bdbCQAAAPi7B7hRo0ayf/9+ueGGG+Suu+4yQyL0DHDbt2839YABAACAAtMDrGd+u/XWW83Z4F588UXfbxUAAAAQSD3AWv5s586dvt8aAAAAIFCHQPTu3VtmzZrl260BAAAAAnUSXEpKinzwwQeyevVqadmypTkNsrtJkyb5YvsAAAAA/wbgP/74Q2rUqCG7d++WFi1amGU6Gc6dlkQDAAAACkQA1jO9HTt2TNauXes69fFbb70lFStWvBzbBwAAAPh3DLBlWR7Xv/76a1MCDQAAACjQk+AyCsQAAABAgQrAOr7Xe4wvY34BAABQYMcAa4/vQw89JCEhIeZ6QkKCDBw4ME0ViEWLFvluKwEAAAB/BeA+ffqkqQcMAAAAFNgAPHv27MuzJQAAAEB+mAQHAAAA5CcEYAAAADgKARgAAACOQgAGAACAoxCAAQAA4CgEYAAAADgKARgAAACOQgAGAACAoxCAAQAA4Ch+DcDjxo2Ta665RkqWLCkVKlSQrl27yr59+zzWSUhIkEGDBkm5cuWkRIkS0qNHDzlx4oTHOocPH5YuXbpIaGioaWfo0KGSkpLisc66deukRYsWEhISIrVq1ZI5c+bkyWMEAABAYPFrAF6/fr0Jt5s2bZJVq1ZJcnKydOrUSeLj413rPP3007JkyRJZuHChWf/o0aPSvXt31+0XL1404TcpKUk2bNggc+fONeF25MiRrnWioqLMOu3atZMdO3bI4MGDZcCAAbJixYo8f8wAAADwr2B/3vk333zjcV2Dq/bgbtu2TW666SY5e/aszJo1Sz7++GNp3769WWf27NlSv359E5pbtWolK1eulL1798rq1aulYsWK0qxZM3nppZfkueeek9GjR0vRokVlxowZUrNmTZk4caJpQ3//hx9+kMmTJ0vnzp398tgBAADgwADsTQOvKlu2rPmpQVh7hTt27Ohap169elK9enXZuHGjCcD6s3Hjxib82jTUPvbYY7Jnzx5p3ry5Wce9DXsd7QnOSGJiornYYmNjzc/U1FRzAQB/sCxLgoKCJEhEgsTKVVumjaAg02ZB/Fzz9b4qVKgQ+x0IcFl9TwUH0gZrIG3Tpo00atTILDt+/LjpwS1durTHuhp29TZ7Hffwa99u35bZOhpqL1y4IMWLF093fPKYMWPSLI+JiTHjkgHAH+Li4qRWzUipECYSWuR/X9JzokSYSHDNSNPmyZMnpaDx5b4KLhMi8Q3rS7XwwlKa/Q4ELH1f5asArGOBd+/ebYYmBILhw4fLkCFDXNc1LFerVk0iIiIkPDzcr9sGwLnOnTsnB6OiJaW+SHhYSK7aio0XORQV7ZqIXND4cl8dPZ0ov+z5VcLbXJSkMux3IFAVK1Ys/wTgxx9/XJYuXSrfffedVK1a1bW8UqVKZnLbmTNnPHqBtQqE3mavs2XLFo/27CoR7ut4V47Q6xpk0+v9VVotQi/e9BCYXgDAH+xD53oQ3jIH03POchsmUBA/13y9r/RIJfsdCGxZfU/59Z2nHwAafhcvXizffvutmajmrmXLllKkSBFZs2aNa5mWSdOyZ61btzbX9eeuXbs8DiNpRQkNtw0aNHCt496GvY7dBgAAAJwj2N/DHrTCw5dffmkOBdljdkuVKmV6ZvVn//79zVAEnRinofaJJ54wwVUnwCktm6ZB94EHHpAJEyaYNkaMGGHatntwBw4cKFOnTpVhw4ZJv379TNhesGCBLFu2zJ8PHwAAAH7g1x7gd955x1R+aNu2rVSuXNl1+fTTT13raKmyO+64w5wAQ0uj6XCGRYsWuW4vXLiwGT6hPzUY9+7dWx588EEZO3asax3tWdawq72+TZs2NeXQZs6cSQk0AAAABwr29xCIrAxmnjZtmrlkJDIyUpYvX55pOxqyt2/fnqPtBAAAQMHB6HsAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjuLXUyEDAPwrOSlJoqOjfdJWeHi4RERE+KQtALicCMAA4FCJ587Koag/ZPALoyUkJCTX7ZUtGSrzZs8kBAMIeARgAHCo5MQLkhoULOVbdZdyVSJz1Vb8qRMSs/FziY2NJQADCHgEYABwuNAyERJeoWqu24nxydYAwOXHJDgAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAowf7eAAAAnCY5KUmio6N90lZ4eLhERET4pC3AKQjAAADkocRzZ+VQ1B8y+IXREhISkuv2ypYMlXmzZxKCgWwgAAMAkIeSEy9IalCwlG/VXcpVicxVW/GnTkjMxs8lNjaWAAxkAwEYAAA/CC0TIeEVqua6nRifbA3gLEyCAwAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKMQgAEAAOAoBGAAAAA4CgEYAAAAjkIABgAAgKP4PQB/9913cuedd0qVKlUkKChIvvjiC4/bLcuSkSNHSuXKlaV48eLSsWNHOXDggMc6p06dkl69ekl4eLiULl1a+vfvL+fOnfNYZ+fOnXLjjTdKsWLFpFq1ajJhwoQ8eXwAAAAILH4PwPHx8dK0aVOZNm1aurdrUH3rrbdkxowZsnnzZgkLC5POnTtLQkKCax0Nv3v27JFVq1bJ0qVLTah+5JFHXLfHxsZKp06dJDIyUrZt2yavv/66jB49Wt577708eYwAAAAIHMH+3oDbbrvNXNKjvb9vvvmmjBgxQu666y6z7MMPP5SKFSuanuJ//vOf8uuvv8o333wjP/30k1x99dVmnbfffltuv/12eeONN0zP8kcffSRJSUnywQcfSNGiRaVhw4ayY8cOmTRpkkdQdpeYmGgu7iFapaammgsA+IN+LurRsiARCRIrV21pG4UKFfJZW7pdun2B8hkZyPuqIO93wJ+y+j7wewDOTFRUlBw/ftwMe7CVKlVKrrvuOtm4caMJwPpThz3Y4Vfp+vrhoj3G3bp1M+vcdNNNJvzatBf5tddek9OnT0uZMmXS3Pe4ceNkzJgxaZbHxMR49D4DQF6Ki4uTWjUjpUKYSGiR/31Jz4ngMiES37C+VAsvLKVz2VaJMJHgmpFm+06ePCmBIFD3VUHf74A/6Xsh3wdgDb9Ke3zd6XX7Nv1ZoUIFj9uDg4OlbNmyHuvUrFkzTRv2bekF4OHDh8uQIUM8eoB17HBERIQZawwA/qDzGw5GRUtKfZHwsJBctXX0dKL8sudXCW9zUZLK5K6t2HiRQ1HRUrJkyTSfyf4SqPuqoO93wJ90rle+D8D+FBISYi7etGdZLwDgD/bhbj1wbpkD4Dln/f/Dhb5qyx5yECifkYG8rwryfgf8Kavvg4B+t1SqVMn8PHHihMdyvW7fpj+9D/ukpKSYyhDu66TXhvt9AAAAwBkCOgDrsAUNqGvWrPEYiqBje1u3bm2u688zZ86Y6g62b7/91ny71rHC9jpaGSI5Odm1jlaMqFu3brrDHwAAAFBwFQqEMVpakUEv9sQ3/f/hw4fNIZ3BgwfLyy+/LF999ZXs2rVLHnzwQVPZoWvXrmb9+vXry6233ioPP/ywbNmyRX788Ud5/PHHzQQ5XU/df//9ZgKc1gfWcmmffvqpTJkyxWOMLwAAAJzB72OAt27dKu3atXNdt0Npnz59ZM6cOTJs2DBTK1jLlWlP7w033GDKnrkPctYyZxp6O3ToYMZ+9OjRw9QOdq8csXLlShk0aJC0bNlSypcvb06ukVEJNAAAABRcfg/Abdu2NQP4M6K9wGPHjjWXjGjFh48//jjT+2nSpIl8//33udpWAAAA5H9+HwIBAAAA5CUCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcBQCMAAAAByFAAwAAABHIQADAADAUQjAAAAAcJRgf28A8p+YmBiJjY3NdTvh4eESERHhk20CAADIKgIwsh1+e/cdIKfizue6rbIlQ2Xe7JmEYAAAkKcIwMgW7fnV8BvRuoeEla2Y43biT52QmI2fm/YIwHACXx05iY6OlpTkFJ9sEwA4FQEYOaLhN7xC1Vy1EeOzrQGcc+Qk4cJ5+fOvY1I9Odkn2wYATkQABoB8cuREnfx9t0Qf+UAuphCAASCnCMAAkI+OnJz7+7jPtgcAnIoyaAAAAHAUAjAAAAAchQAMAAAARyEAAwAAwFEIwAAAAHAUAjAAAAAchQAMAAAARyEAAwAAwFEIwAAAAHAUAjAAAAAchQAMAAAARyEAAwAAwFEIwAAAAHAUAjAAAAAchQAMAAAARwn29wYAvhATEyOxsbE+aSs8PFwiIiJ80hYAAAg8BGAUiPDbu+8AORV33iftlS0ZKvNmzyQEAwBQQBGAke9pz6+G34jWPSSsbMVctRV/6oTEbPzctEkABgCgYCIAo8DQ8BteoWqu24nxydYAAIBARQAGUOAwJhwAkBkCMIAChTHhAIBLIQADKFAYEw4AuBQCMIACiTHhAICMcCIMAAAAOAoBGAAAAI5CAAYAAICjEIABAADgKARgAAAAOApVIAAABfJEJtHR0ZKSnOKTbQJQsBCAAQAF8kQmCRfOy59/HZPqyck+2TYABQcBGAAykZyUZHoSc4OeSP+cyOTk77sl+sgHcjGFAAzAEwEYADKQeO6sHIr6Qwa/MFpCQkJy3A49kf45kcm5v4/7bHsAFCwEYADIQHLiBUkNCpbyrbpLuSqROW6HnkgACCwEYAC4hNAyEbnqjaQnEgACC2XQAAAA4CgEYAAAADgKQyAAh9VGDQ8Pl4iICAk01H4FAOQVAjDydXkppwQeX9ZGLVsyVObNnhlQIZjarwCAvEQAdohA613zVXkppwQeX9VGjT91QmI2fm7aC6QATO1XAEBeclQAnjZtmrz++uty/Phxadq0qbz99tty7bXXSkEXiL1rviovdTkCj696pi/HcANf1EaNkcBF7df8jZOGAMgvHBOAP/30UxkyZIjMmDFDrrvuOnnzzTelc+fOsm/fPqlQoYIUZIHcu5bb8lK+Djy+7JlWJYoWltdeGSvlypULmFDgy4CflJQkRYsWzXU7hJ78j5OGAMhPHBOAJ02aJA8//LD07dvXXNcgvGzZMvnggw/k+eefFyegdy1ve6ZP/XlQti14SwY8+WzADPPwZcDXIP3X4WipGllTgovk7qOE0JP/cdKQ/M9XQ+V8ffQrULcL+ZsjArD2Um3btk2GDx/uWlaoUCHp2LGjbNy4Md3fSUxMNBfb2bNnzc8zZ85IampqHmz1/93X6dOnc93OkSNHJDkxUc4eOyQpCbkbBhEX86cE6c8TR6RIkP/buVxtXUxKyPW+Sjx3RqygYAm56lopXTZ3RxlOHzskqUeOypm//pBCF3MeDE79ecCn25R06LAE12wZMI8vUF9bgbhNgfreuZicENCPL9Daij99UpITEmTPnj25Con6t2bMq+PlXIJvjsSUCCkso14cLmXKlMlVO4G6Xcge3d+lS5eWvGC/DyzLynS9IOtSaxQAR48elSuuuEI2bNggrVu3di0fNmyYrF+/XjZv3pzmd0aPHi1jxozJ4y0FAACALzr/qlat6uwe4JzQ3mIdM2zTXt9Tp06ZsZxBQUE++YZSrVo18wTpIRlcGvss+9hn2cc+yz72Wfawv7KPfZZ9Tt1nlmVJXFycVKlSJdP1HBGAy5cvL4ULF5YTJ054LNfrlSpVSvd3dHyk9xjJy9F9ry9KJ70wfYF9ln3ss+xjn2Uf+yx72F/Zxz7LPifus1KlSl1yHUecCllnqbds2VLWrFnj0aOr192HRAAAAKDgc0QPsNLhDH369JGrr77a1P7VMmjx8fGuqhAAAABwBscE4J49e5pSKiNHjjQnwmjWrJl88803UrFi7uri5pQOrxg1apRPas06Bfss+9hn2cc+yz72Wfawv7KPfZZ97LPMOaIKBAAAAOCoMcAAAACAjQAMAAAARyEAAwAAwFEIwAAAAHAUArCfTJs2TWrUqCHFihWT6667TrZs2SIF0XfffSd33nmnOSOLnkHviy++8Lhd52BqZY7KlStL8eLFpWPHjnLgwAGPdfQMfL169TKFvPVkJP3795dz5855rLNz50658cYbzf7UM99MmDAhzbYsXLhQ6tWrZ9Zp3LixLF++XALNuHHj5JprrpGSJUtKhQoVpGvXrrJv3z6PdRISEmTQoEHmrIQlSpSQHj16pDnJy+HDh6VLly4SGhpq2hk6dKikpKR4rLNu3Tpp0aKFmSFcq1YtmTNnTr58nb7zzjvSpEkTV7F3re399ddfu25nf2Vu/Pjx5r05ePBg1zL2WVqjR482+8n9op8nNvZZWn/99Zf07t3b7BP9fNfP3a1bt7pu5/Pfkz6n3q8xvejrSvEa8zGtAoG8NX/+fKto0aLWBx98YO3Zs8d6+OGHrdKlS1snTpywCprly5dbL774orVo0SKtNmItXrzY4/bx48dbpUqVsr744gvrl19+sf7xj39YNWvWtC5cuOBa59Zbb7WaNm1qbdq0yfr++++tWrVqWffdd5/r9rNnz1oVK1a0evXqZe3evdv65JNPrOLFi1vvvvuua50ff/zRKly4sDVhwgRr79691ogRI6wiRYpYu3btsgJJ586drdmzZ5vHsWPHDuv222+3qlevbp07d861zsCBA61q1apZa9assbZu3Wq1atXKuv766123p6SkWI0aNbI6duxobd++3TwH5cuXt4YPH+5a548//rBCQ0OtIUOGmP3x9ttvm/3zzTff5LvX6VdffWUtW7bM2r9/v7Vv3z7rhRdeMM+t7kPF/srYli1brBo1alhNmjSxnnrqKddy9llao0aNsho2bGgdO3bMdYmJiXHdzj7zdOrUKSsyMtJ66KGHrM2bN5vHtmLFCuvgwYOudfj893Ty5EmP19eqVavM3821a9ea23mN+RYB2A+uvfZaa9CgQa7rFy9etKpUqWKNGzfOKsi8A3BqaqpVqVIl6/XXX3ctO3PmjBUSEmI+xJS+QfX3fvrpJ9c6X3/9tRUUFGT99ddf5vr06dOtMmXKWImJia51nnvuOatu3bqu6/fee6/VpUsXj+257rrrrEcfffQyPVrffSDq41+/fr1r/+gH98KFC13r/Prrr2adjRs3muv6oVeoUCHr+PHjrnXeeecdKzw83LWPhg0bZv6Yu+vZs6cJ4AXhdaqvh5kzZ7K/MhEXF2fVrl3b/JG9+eabXQGYfZZxANYglh72WVr6GXzDDTdkeDuf/5em78mrrrrK7CteY77HEIg8lpSUJNu2bTOHemyFChUy1zdu3ChOEhUVZU5K4r4v9PzderjF3hf6Uw976Rn8bLq+7rPNmze71rnpppvMKa9tnTt3NkMHTp8+7VrH/X7sdQJ9n589e9b8LFu2rPmpr53k5GSPx6KH9apXr+6xz/QQn/tJXvSxxsbGyp49e7K0P/Lr6/TixYsyf/58c5ZHHQrB/sqYHkrVQ6Xej4t9ljE9PK/Dua688kpzWF4PNyv2WVpfffWV+dy+5557zKH45s2by/vvv++6nc//zOlzPW/ePOnXr58ZBsFrzPcIwHnsv//9r/kj7X0GOr2uHwZOYj/ezPaF/tQPT3fBwcEmELqvk14b7veR0TqBvM9TU1PNuMw2bdpIo0aNzDLdXv2g1z8Kme2znO4P/aC8cOFCvnud7tq1y4yJ0zFtAwcOlMWLF0uDBg3YXxnQLwk///yzGXPujX2WPg1mOlZSzyCq4841wOm407i4OPZZOv744w+zn2rXri0rVqyQxx57TJ588kmZO3euuZ3P/8zpfJkzZ87IQw89ZK7zGvM9x5wKGchvtIdu9+7d8sMPP/h7UwJe3bp1ZceOHabH/LPPPpM+ffrI+vXr/b1ZAenIkSPy1FNPyapVq8wEF2TNbbfd5vq/TrrUQBwZGSkLFiwwE7iQ9gu89ty++uqr5rr2AOvn2YwZM8z7E5mbNWuWec3pEQdcHvQA57Hy5ctL4cKF08zc1OuVKlUSJ7Efb2b7Qn+ePHnS43ad0aozg93XSa8N9/vIaJ1A3eePP/64LF26VNauXStVq1Z1Ldft1UNU2jOQ2T7L6f7Qmdb6xzy/vU61Z0RnM7ds2dL0ajZt2lSmTJnC/kqHHt7U95TOAtfeNL3ol4W33nrL/F97ethnl6Y9cXXq1JGDBw/yOkuHVnbQozDu6tev7xo2wud/xqKjo2X16tUyYMAA1zJeY75HAPbDH2r9I71mzRqPb8p6XccsOknNmjXNG8p9X+hhGB3bZe8L/alveP2jbfv222/NPtMeGHsdLbem46Ns2rulvYJlypRxreN+P/Y6gbbPda6ghl89hK+PU/eRO33tFClSxOOx6Fg3/aPivs90SID7Hw59rPoBZ/9ButT+yO+vU93WxMRE9lc6OnToYB6v9pjbF+2p0zGt9v/ZZ5empbh+//13E/R4naWlQ7e8Szju37/f9JorPv8zNnv2bDP0Q8fo23iNXQaXYWIdLkFLjOhM1zlz5phZro888ogpMeI+c7Og0JnmWo5FL/pymzRpkvl/dHS0qwyOPvYvv/zS2rlzp3XXXXelWwanefPmppTODz/8YGauu5fB0dmxWgbngQceMGVwdP9qmRfvMjjBwcHWG2+8YWbO6ozuQCyD89hjj5myQOvWrfMoh3P+/HnXOloKR0ujffvtt6YUTuvWrc3FuxROp06dTCk1LW8TERGRbimcoUOHmv0xbdq0dEvh5IfX6fPPP2+qZERFRZnXkF7XWeIrV640t7O/Ls29CoRin6X1zDPPmPelvs7080RLTWmJKa3UothnaUvs6WfuK6+8Yh04cMD66KOPzGObN2+eax0+/9PSigv6OtJKFt54jfkWAdhPtPaevpC11p6WHNEahwWR1i/U4Ot96dOnj7ldy7v8+9//Nh9g+obr0KGDqeXq7u+//zYfeCVKlDDlXPr27WuCtTutIakld7SNK664wnyweluwYIFVp04ds8+1DIzWjg006e0rvWhtYJv+cfjXv/5lSv/oB1m3bt1MSHZ36NAh67bbbjP1MPWPtP7xTk5OTvPcNGvWzOyPK6+80uM+8tPrtF+/fqbeqG6jftjra8gOv4r9lf0AzD5LS0tFVa5c2WynfsbodfeatuyztJYsWWICmX4u16tXz3rvvfc8bufzPy2tlayf+d77QfEa860g/edy9CwDAAAAgYgxwAAAAHAUAjAAAAAchQAMAAAARyEAAwAAwFEIwAAAAHAUAjAAAAAchQAMAAAARyEAAwAAwFEIwABwGaxbt06CgoLkzJkzGa4zZ84cKV26dK7vq0aNGvLmm2+KLx06dMhs/44dO3zaLgAEAgIwgHznyJEj0q9fP6lSpYoULVpUIiMj5amnnpK///7bL9vTtm1bGTx4sMey66+/Xo4dOyalSpUSJ9OTjb733nty3XXXSYkSJUzgv/rqq01gP3/+vASqhQsXSr169aRYsWLSuHFjWb58ucftixYtkk6dOkm5cuX4ogDkQwRgAPnKH3/8YQLUgQMH5JNPPpGDBw/KjBkzZM2aNdK6dWs5deqUBAIN5pUqVTLhyMkeeOAB8+XgrrvukrVr15qg+O9//1u+/PJLWblyZY7bTU5Olstlw4YNct9990n//v1l+/bt0rVrV3PZvXu3a534+Hi54YYb5LXXXrts2wHgMrIAIB+59dZbrapVq1rnz5/3WH7s2DErNDTUGjhwoGuZfsQtXrzYY71SpUpZs2fPdl0fNmyYVbt2bat48eJWzZo1rREjRlhJSUmu20eNGmU1bdrU+vDDD63IyEgrPDzc6tmzpxUbG2tu79Onj7kf90tUVJS1du1a8//Tp0+72tL7rVatmrmvrl27Wm+88YbZHtvBgwetf/zjH1aFChWssLAw6+qrr7ZWrVrlsf0nTpyw7rjjDqtYsWJWjRo1rHnz5pntmjx5smsdvc/+/ftb5cuXt0qWLGm1a9fO2rFjR6b7dfPmzVazZs2skJAQq2XLltaiRYvM9m/fvt21zq5du8z+123Tbezdu7cVExOTYZuffvqpaeOLL75Ic1tqaqp15swZ8/8tW7ZYHTt2tMqVK2f270033WRt27bNY31tZ/r06dadd95pnmd9XtRLL71kRUREWCVKlDCP+bnnnjPPly0rbXu79957rS5dungsu+6666xHH300zbr6XHvvJwCBjx5gAPmG9u6uWLFC/vWvf0nx4sU9btPe1l69esmnn35qDrtnVcmSJc1Y3L1798qUKVPk/fffl8mTJ3us8/vvv8sXX3whS5cuNZf169fL+PHjzW36O9rz/PDDD5shD3qpVq1amvvZvHmz6VF8/PHHTS9ou3bt5OWXX/ZY59y5c3L77beb3mztebz11lvlzjvvlMOHD7vWeeihh8wQEO1N/eyzz2T69Oly8uRJj3buueces+zrr7+Wbdu2SYsWLaRDhw4Z9o7r/d5xxx3SoEEDs/7o0aPl2Wef9VhHxzK3b99emjdvLlu3bpVvvvlGTpw4Iffee2+G+/ajjz6SunXrmt5fb9ozbg8PiYuLkz59+sgPP/wgmzZtktq1a5v9oMvd6XZ169ZNdu3aZYbAaPuvvPKK6YXV7a5evbq88847Hr+T1bbdbdy4UTp27OixrHPnzmY5gALC3wkcALJq06ZN6fbq2iZNmmRu117SrPYAe3v99ddND6hNexq1x9Hu8VVDhw41PYK2m2++2Xrqqac82vHuAb7vvvus22+/3WMd7Ul27wFOT8OGDa23337b/H/fvn2mTe3VtP36669mmd0D/P3335uezoSEBI92rrrqKuvdd99N9z50ufaQXrhwwbXsnXfe8ejZ1J7WTp06efzekSNHzDq6XempX7++6dHOrosXL5qe6yVLlriW6f0MHjzYYz19DgYNGuSxrE2bNh49wFlp21uRIkWsjz/+2GPZtGnTTK+3N3qAgfyJHmAA+c6lenh1/G1WaY9xmzZtTA+yTtIaMWKER4+rXWVBe4ptlStXTtPreim//vqrmQjmTnuOvXtitee1fv36ZrKYbo/+nr09+v/g4GBp2bKl63d0opZ7JYlffvnFtKOTs/T37UtUVJTpyc5o25o0aWImfGW0bdqu9jq7t6n3rTJqN6s98dqTrD3o2jurvcLh4eHmMXg/Dzr2292+ffvk2muv9VjmfT2rbQNwlmB/bwAAZFWtWrXMoXMNbHoo3Jsuj4iIcAVCXdc7hLlPntJD2jpsYsyYMeYQtwak+fPny8SJEz1+p0iRIh7Xtd3U1FQfPzox4XfVqlXyxhtvmMeqwzzuvvtuSUpKynIbGu40oGsZNm+5Kbmm7epwjPQmfen9padOnTry22+/XbJtHaKgFTx0OIlW9AgJCTEB3Ptxh4WFZXu7s9q2O/0ypMHZnV7X5QAKBnqAAeQb2qt5yy23mHGvFy5c8Ljt+PHjZkyojpG1aRjWMbk2rRzhXnpLZ/trKHrxxRdN76L2EkZHR2d7u7TH+eLFi5muo726Og7YnY5Jdffjjz+a7ddwr6W3NHBpPV6b9rimpKSY8a7uvaDutYZ1vK/uC+0p1hDtfilfvnyG27Zz505JSEjIcNu03T179pjecO92Mwqm999/v+zfv99UfPCmX0zOnj3retxPPvmkGZvbsGFDE1L/+9//yqXo+OKffvrJY5n39Zy0rQFZx2G70y8m3r3iAPIvAjCAfGXq1KmSmJhoemy/++47MyFMJ2RpMNYex5EjR7rW1Ulbur5OKNOJWwMHDvTozdXAq4fCtddXD+O/9dZbsnjx4mxvk4ZCDbcaVjVcpdc7rCFMt1N7dzWI63bpdXe6PVpfVifJ6ZADDZDubWng04lxjz76qLk/DcIDBgzwmBCok7c0qGnZLi0zptukQV9Dvu6D9Oj9aK+2DhXQyYBa81a3092gQYPMJDotD6YhU/eXTkjs27dvhuFfJ8j17NnT/M6rr75q7l+/YOhEQt1OHVJhP+7//Oc/pgdfH5f2yntPckzPE088IbNmzZK5c+eafaqTCjXIu5eey0nbWlNanxs9EqA92Dr5TrddJzDadF/o86T7y/4iotf1yweAfMDfg5ABILt04pGWH6tYsaIVFBRkJiF1797dio+P91jvr7/+MhO3tGyXljpbvnx5mklwOqFNJ4BpGS2dlKaTydwnptll0NzpOlp6zKaTwFq1amXKm2VWBm3WrFmmhJuup+W8vMug6e9pyTK9XculTZ06Nc0EOy33piW6tFxZ9erVXeXZ3Mug6YS9J554wqpSpYqZ0KVt9erVyzp8+HCG+3Tjxo3mcRYtWtSUQ/v888/TTO7av3+/1a1bN6t06dJmG+vVq2cmpmlJs8wmnemEumuuucZMJtQJejrJcMqUKa5Sdj///LMp+aal3fR5WrhwYZrHlNHkx7Fjx5pyb/r89evXz3ryySfNc2HLStvpWbBggVWnTh2zP3Qi4rJlyzxu19eQd/k7vdjl2QAEtiD9x98hHAByY9SoUTJp0iRzmLpVq1b+3hz4kR4J0KEj2usLABlhEhyAfE8nsekwBB23qlUAChVidJcT6HhuPQugDocpXLiwOTPg6tWrzRchAMgMPcAAgHxJJ0JqZQod460T+HSMtJax6969u783DUCAIwADAADAUThOCAAAAEchAAMAAMBRCMAAAABwFAIwAAAAHIUADAAAAEchAAMAAMBRCMAAAABwFAIwAAAAxEn+H1MO7L7R99B/AAAAAElFTkSuQmCC", "text/plain": ["<Figure size 800x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== Resultados do Teste de Normalidade (Shapiro<PERSON><PERSON>ilk) ===\n", "\n", "                                    Estatística p-valor  \\\n", "Valor Total da Prestação do Serviço    0.976575     0.0   \n", "Valor total da carga                   0.215195     0.0   \n", "Quantidade de Carga 01                 0.776262     0.0   \n", "\n", "                                                         <PERSON><PERSON><PERSON><PERSON>  \n", "Valor Total da Prestação do Serviço  Não segue distribuição normal  \n", "Valor total da carga                 Não segue distribuição normal  \n", "Quantidade de Carga 01               Não segue distribuição normal  \n"]}, {"name": "stderr", "output_type": "stream", "text": ["c:\\Python313\\Lib\\site-packages\\scipy\\stats\\_axis_nan_policy.py:579: UserWarning: scipy.stats.shapiro: For N > 5000, computed p-value may not be accurate. Current N is 18228.\n", "  res = hypotest_fun_out(*samples, **kwds)\n"]}], "source": ["# === 1. <PERSON><PERSON><PERSON> o arquivo ===\n", "df = pd.read_csv(\n", "    r\"c:\\Users\\<USER>\\OneDrive\\Documentos\\GitHub\\2025-2A-T19-IN03-G05\\notebooks\\Dados\\Graneis-22-23-24(in).csv\",\n", "    sep=\";\"\n", ")\n", "\n", "# === 2. <PERSON><PERSON><PERSON><PERSON> as vari<PERSON><PERSON>is necessárias ===\n", "variaveis = df[[\n", "    \"Valor Total da Prestação do Serviço\",\n", "    \"Valor total da carga\",\n", "    \"Quantidade de Carga 01\"\n", "]].copy()\n", "\n", "# === 3. <PERSON><PERSON><PERSON> para número (tirar pontos de milhar e trocar vírgula por ponto) ===\n", "for coluna in variaveis.columns:\n", "    variaveis[coluna] = (variaveis[coluna]\n", "                         .astype(str)\n", "                         .str.replace(\".\", \"\", regex=False)\n", "                         .str.replace(\",\", \".\", regex=False)\n", "                         .astype(float))\n", "\n", "# === 4. <PERSON><PERSON><PERSON> valores nulos (se houver) ===\n", "variaveis = variaveis.dropna()\n", "\n", "# === 5. Cal<PERSON> média e mediana ===\n", "medidas = pd.DataFrame({\n", "    \"Média\": round(variaveis.mean(), 2),\n", "    \"Mediana\": round(variaveis.median(), 2)\n", "})\n", "print(\"=== Média e Mediana ===\\n\")\n", "print(medidas)\n", "\n", "# === 6. <PERSON><PERSON><PERSON> histogramas ===\n", "for coluna in variaveis.columns:\n", "    plt.figure(figsize=(8, 5))\n", "    plt.hist(variaveis[coluna], bins=30, edgecolor=\"black\", alpha=0.7)\n", "    plt.title(f\"Histograma - {coluna}\")\n", "    plt.xlabel(coluna)\n", "    plt.ylabel(\"Frequência\")\n", "    plt.grid(axis=\"y\", alpha=0.3)\n", "    plt.show()\n", "\n", "# === 7. <PERSON><PERSON><PERSON><PERSON> <PERSON>-<PERSON><PERSON>k ===\n", "resultados = {}\n", "for coluna in variaveis.columns:\n", "    stat, p = shapiro(variaveis[coluna])\n", "    resultados[coluna] = {\n", "        \"Estatística\": stat,\n", "        \"p-valor\": p,\n", "        \"Conclusão\": \"Segue distribuição normal\" if p > 0.05 else \"Não segue distribuição normal\"\n", "    }\n", "\n", "# === 8. <PERSON><PERSON><PERSON> tabela de resultados ===\n", "tabela_resultados = pd.DataFrame(resultados).T\n", "print(\"\\n=== Resultados do Teste de Normalidade (Shapiro<PERSON><PERSON><PERSON><PERSON>) ===\\n\")\n", "print(tabela_resultados)"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.6"}}, "nbformat": 4, "nbformat_minor": 5}