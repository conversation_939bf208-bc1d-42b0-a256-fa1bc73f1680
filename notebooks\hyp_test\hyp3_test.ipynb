{"cells": [{"cell_type": "code", "execution_count": 1, "id": "a71dfde7", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "from scipy import stats"]}, {"cell_type": "code", "execution_count": 2, "id": "be4347de", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Hipótese 3: A sazonalidade durante o ano impacta os valores de frete?\n"]}], "source": ["print(\"Hipótese 3: A sazonalidade durante o ano impacta os valores de frete?\")"]}, {"cell_type": "code", "execution_count": 3, "id": "4513fe35", "metadata": {}, "outputs": [{"ename": "FileNotFoundError", "evalue": "[Errno 2] No such file or directory: 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'", "output_type": "error", "traceback": ["\u001b[31m---------------------------------------------------------------------------\u001b[39m", "\u001b[31mFileNotFoundError\u001b[39m                         <PERSON><PERSON> (most recent call last)", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 50\u001b[39m\n\u001b[32m     47\u001b[39m cte_file_path = \u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mCTE-22-23-24(in).csv\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m     48\u001b[39m graneis_file_path = \u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGraneis-22-23-24(in).csv\u001b[39m\u001b[33m'\u001b[39m\n\u001b[32m---> \u001b[39m\u001b[32m50\u001b[39m df_final = \u001b[43mload_and_preprocess_seasonality_data\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcte_file_path\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mgraneis_file_path\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m     52\u001b[39m \u001b[38;5;66;03m# Salvar o dataframe processado\u001b[39;00m\n\u001b[32m     53\u001b[39m df_final.to_csv(\u001b[33m'\u001b[39m\u001b[33mc:\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mUsers\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mInteli\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mOneDrive\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDocumentos\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mGitHub\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33m2025-2A-T19-IN03-G05\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mnotebooks\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mDados\u001b[39m\u001b[38;5;130;01m\\\\\u001b[39;00m\u001b[33mprocessed_seasonality_data.csv\u001b[39m\u001b[33m'\u001b[39m, index=\u001b[38;5;28;01mFalse\u001b[39;00m)\n", "\u001b[36mCell\u001b[39m\u001b[36m \u001b[39m\u001b[32mIn[3]\u001b[39m\u001b[32m, line 3\u001b[39m, in \u001b[36mload_and_preprocess_seasonality_data\u001b[39m\u001b[34m(cte_file, graneis_file)\u001b[39m\n\u001b[32m      1\u001b[39m \u001b[38;5;28;01mdef\u001b[39;00m\u001b[38;5;250m \u001b[39m\u001b[34mload_and_preprocess_seasonality_data\u001b[39m(cte_file, graneis_file):\n\u001b[32m      2\u001b[39m     \u001b[38;5;66;03m# <PERSON>egar os arquivos CSV\u001b[39;00m\n\u001b[32m----> \u001b[39m\u001b[32m3\u001b[39m     df_cte = \u001b[43mpd\u001b[49m\u001b[43m.\u001b[49m\u001b[43mread_csv\u001b[49m\u001b[43m(\u001b[49m\u001b[43mcte_file\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43msep\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m;\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mdecimal\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m,\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mon_bad_lines\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mskip\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\n\u001b[32m      4\u001b[39m     df_graneis = pd.read_csv(graneis_file, sep=\u001b[33m\"\u001b[39m\u001b[33m;\u001b[39m\u001b[33m\"\u001b[39m, decimal=\u001b[33m\"\u001b[39m\u001b[33m,\u001b[39m\u001b[33m\"\u001b[39m, on_bad_lines=\u001b[33m\"\u001b[39m\u001b[33mskip\u001b[39m\u001b[33m\"\u001b[39m)\n\u001b[32m      6\u001b[39m     \u001b[38;5;66;03m# Renomear colunas\u001b[39;00m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1026\u001b[39m, in \u001b[36mread_csv\u001b[39m\u001b[34m(filepath_or_buffer, sep, delimiter, header, names, index_col, usecols, dtype, engine, converters, true_values, false_values, skipinitialspace, skiprows, skipfooter, nrows, na_values, keep_default_na, na_filter, verbose, skip_blank_lines, parse_dates, infer_datetime_format, keep_date_col, date_parser, date_format, dayfirst, cache_dates, iterator, chunksize, compression, thousands, decimal, lineterminator, quotechar, quoting, doublequote, escapechar, comment, encoding, encoding_errors, dialect, on_bad_lines, delim_whitespace, low_memory, memory_map, float_precision, storage_options, dtype_backend)\u001b[39m\n\u001b[32m   1013\u001b[39m kwds_defaults = _refine_defaults_read(\n\u001b[32m   1014\u001b[39m     dialect,\n\u001b[32m   1015\u001b[39m     delimiter,\n\u001b[32m   (...)\u001b[39m\u001b[32m   1022\u001b[39m     dtype_backend=dtype_backend,\n\u001b[32m   1023\u001b[39m )\n\u001b[32m   1024\u001b[39m kwds.update(kwds_defaults)\n\u001b[32m-> \u001b[39m\u001b[32m1026\u001b[39m \u001b[38;5;28;01mreturn\u001b[39;00m \u001b[43m_read\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:620\u001b[39m, in \u001b[36m_read\u001b[39m\u001b[34m(filepath_or_buffer, kwds)\u001b[39m\n\u001b[32m    617\u001b[39m _validate_names(kwds.get(\u001b[33m\"\u001b[39m\u001b[33mnames\u001b[39m\u001b[33m\"\u001b[39m, \u001b[38;5;28;01mNone\u001b[39;00m))\n\u001b[32m    619\u001b[39m \u001b[38;5;66;03m# Create the parser.\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m620\u001b[39m parser = \u001b[43mTextFileReader\u001b[49m\u001b[43m(\u001b[49m\u001b[43mfilepath_or_buffer\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[43m*\u001b[49m\u001b[43m*\u001b[49m\u001b[43mkwds\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    622\u001b[39m \u001b[38;5;28;01mif\u001b[39;00m chunksize \u001b[38;5;129;01mor\u001b[39;00m iterator:\n\u001b[32m    623\u001b[39m     \u001b[38;5;28;01m<PERSON>urn\u001b[39;00m parser\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1620\u001b[39m, in \u001b[36mTextFileReader.__init__\u001b[39m\u001b[34m(self, f, engine, **kwds)\u001b[39m\n\u001b[32m   1617\u001b[39m     \u001b[38;5;28mself\u001b[39m.options[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m] = kwds[\u001b[33m\"\u001b[39m\u001b[33mhas_index_names\u001b[39m\u001b[33m\"\u001b[39m]\n\u001b[32m   1619\u001b[39m \u001b[38;5;28mself\u001b[39m.handles: IOHandles | \u001b[38;5;28;01mNone\u001b[39;00m = \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m-> \u001b[39m\u001b[32m1620\u001b[39m \u001b[38;5;28mself\u001b[39m._engine = \u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43m_make_engine\u001b[49m\u001b[43m(\u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43mengine\u001b[49m\u001b[43m)\u001b[49m\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\parsers\\readers.py:1880\u001b[39m, in \u001b[36mTextFileReader._make_engine\u001b[39m\u001b[34m(self, f, engine)\u001b[39m\n\u001b[32m   1878\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m mode:\n\u001b[32m   1879\u001b[39m         mode += \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m\n\u001b[32m-> \u001b[39m\u001b[32m1880\u001b[39m \u001b[38;5;28mself\u001b[39m.handles = \u001b[43mget_handle\u001b[49m\u001b[43m(\u001b[49m\n\u001b[32m   1881\u001b[39m \u001b[43m    \u001b[49m\u001b[43mf\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1882\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1883\u001b[39m \u001b[43m    \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1884\u001b[39m \u001b[43m    \u001b[49m\u001b[43mcompression\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mcompression\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1885\u001b[39m \u001b[43m    \u001b[49m\u001b[43mmemory_map\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mmemory_map\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mFalse\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1886\u001b[39m \u001b[43m    \u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m=\u001b[49m\u001b[43mis_text\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1887\u001b[39m \u001b[43m    \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mencoding_errors\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstrict\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1888\u001b[39m \u001b[43m    \u001b[49m\u001b[43mstorage_options\u001b[49m\u001b[43m=\u001b[49m\u001b[38;5;28;43mself\u001b[39;49m\u001b[43m.\u001b[49m\u001b[43moptions\u001b[49m\u001b[43m.\u001b[49m\u001b[43mget\u001b[49m\u001b[43m(\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43mstorage_options\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\u001b[43m \u001b[49m\u001b[38;5;28;43;01mNone\u001b[39;49;00m\u001b[43m)\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m   1889\u001b[39m \u001b[43m\u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m   1890\u001b[39m \u001b[38;5;28;01massert\u001b[39;00m \u001b[38;5;28mself\u001b[39m.handles \u001b[38;5;129;01mis\u001b[39;00m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;28;01mNone\u001b[39;00m\n\u001b[32m   1891\u001b[39m f = \u001b[38;5;28mself\u001b[39m.handles.handle\n", "\u001b[36mFile \u001b[39m\u001b[32mc:\\Python313\\Lib\\site-packages\\pandas\\io\\common.py:873\u001b[39m, in \u001b[36mget_handle\u001b[39m\u001b[34m(path_or_buf, mode, encoding, compression, memory_map, is_text, errors, storage_options)\u001b[39m\n\u001b[32m    868\u001b[39m \u001b[38;5;28;01melif\u001b[39;00m \u001b[38;5;28misinstance\u001b[39m(handle, \u001b[38;5;28mstr\u001b[39m):\n\u001b[32m    869\u001b[39m     \u001b[38;5;66;03m# Check whether the filename is to be opened in binary mode.\u001b[39;00m\n\u001b[32m    870\u001b[39m     \u001b[38;5;66;03m# Binary mode does not support 'encoding' and 'newline'.\u001b[39;00m\n\u001b[32m    871\u001b[39m     \u001b[38;5;28;01mif\u001b[39;00m ioargs.encoding \u001b[38;5;129;01mand\u001b[39;00m \u001b[33m\"\u001b[39m\u001b[33mb\u001b[39m\u001b[33m\"\u001b[39m \u001b[38;5;129;01mnot\u001b[39;00m \u001b[38;5;129;01min\u001b[39;00m ioargs.mode:\n\u001b[32m    872\u001b[39m         \u001b[38;5;66;03m# Encoding\u001b[39;00m\n\u001b[32m--> \u001b[39m\u001b[32m873\u001b[39m         handle = \u001b[38;5;28;43mopen\u001b[39;49m\u001b[43m(\u001b[49m\n\u001b[32m    874\u001b[39m \u001b[43m            \u001b[49m\u001b[43mhandle\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    875\u001b[39m \u001b[43m            \u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mmode\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    876\u001b[39m \u001b[43m            \u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m=\u001b[49m\u001b[43mioargs\u001b[49m\u001b[43m.\u001b[49m\u001b[43mencoding\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    877\u001b[39m \u001b[43m            \u001b[49m\u001b[43merrors\u001b[49m\u001b[43m=\u001b[49m\u001b[43merrors\u001b[49m\u001b[43m,\u001b[49m\n\u001b[32m    878\u001b[39m \u001b[43m            \u001b[49m\u001b[43mnewline\u001b[49m\u001b[43m=\u001b[49m\u001b[33;43m\"\u001b[39;49m\u001b[33;43m\"\u001b[39;49m\u001b[43m,\u001b[49m\n\u001b[32m    879\u001b[39m \u001b[43m        \u001b[49m\u001b[43m)\u001b[49m\n\u001b[32m    880\u001b[39m     \u001b[38;5;28;01melse\u001b[39;00m:\n\u001b[32m    881\u001b[39m         \u001b[38;5;66;03m# Binary mode\u001b[39;00m\n\u001b[32m    882\u001b[39m         handle = \u001b[38;5;28mopen\u001b[39m(handle, ioargs.mode)\n", "\u001b[31mFileNotFoundError\u001b[39m: [Errno 2] No such file or directory: 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'"]}], "source": ["def load_and_preprocess_seasonality_data(cte_file, graneis_file):\n", "    # Carregar os arquivos CSV\n", "    df_cte = pd.read_csv(cte_file, sep=\";\", decimal=\",\", on_bad_lines=\"skip\")\n", "    df_graneis = pd.read_csv(graneis_file, sep=\";\", decimal=\",\", on_bad_lines=\"skip\")\n", "\n", "    # Renomear colunas\n", "    df_cte = df_cte.rename(columns={\n", "        'Valor CT-e': '<PERSON>or_Frete',\n", "        'Data da Emissão': 'Data_Emissao'\n", "    })\n", "    df_graneis = df_graneis.rename(columns={\n", "        'Valor Total da Prestação do Serviço': '<PERSON>or_Frete_Graneis'\n", "    })\n", "\n", "    # Selecionar colunas relevantes\n", "    df_cte = df_cte[[\"<PERSON><PERSON>\", \"Data_Emissao\", \"Valor_Frete\"]]\n", "    df_graneis = df_graneis[[\"<PERSON><PERSON>\", \"<PERSON>or_Frete_Graneis\"]]\n", "\n", "    # Converter valores de frete para numérico\n", "    df_cte[\"Valor_Frete\"] = pd.to_numeric(df_cte[\"Valor_Frete\"], errors=\"coerce\")\n", "    df_graneis[\"<PERSON>or_Frete_Graneis\"] = pd.to_numeric(df_graneis[\"Valor_Frete_Graneis\"], errors=\"coerce\")\n", "\n", "    # Remover nulos nos valores de frete\n", "    df_cte.dropna(subset=[\"Valor_Frete\"], inplace=True)\n", "    df_graneis.dropna(subset=[\"Valor_Frete_Graneis\"], inplace=True)\n", "\n", "    # Juntar os dataframes\n", "    df_merged = pd.merge(df_cte, df_graneis, on=\"Chave de Acesso\", how=\"outer\")\n", "\n", "    # Unificar colunas de valor\n", "    df_merged[\"Valor_Final_Frete\"] = df_merged[\"Valor_Frete\"].fillna(df_merged[\"Valor_Frete_Graneis\"])\n", "\n", "    # Remover linhas onde o valor final do frete ou a data são nulos\n", "    df_merged.dropna(subset=[\"Valor_Final_Frete\", \"Data_Emissao\"], inplace=True)\n", "\n", "    # Converter 'Data_Emissao' para datetime\n", "    df_merged[\"Data_Emissao\"] = pd.to_datetime(df_merged[\"Data_Emissao\"], format= '%d/%m/%Y', errors=\"coerce\")\n", "    df_merged.dropna(subset=[\"Data_Emissao\"], inplace=True)\n", "\n", "    # Extrair o mês e o trimestre\n", "    df_merged[\"Mes\"] = df_merged[\"Data_Emissao\"].dt.month\n", "    df_merged[\"Trimestre\"] = df_merged[\"Data_Emissao\"].dt.quarter\n", "\n", "    return df_merged\n", "\n", "if __name__ == '__main__':\n", "    cte_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\CTE-22-23-24(in).csv'\n", "    graneis_file_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\Graneis-22-23-24(in).csv'\n", "\n", "    df_final = load_and_preprocess_seasonality_data(cte_file_path, graneis_file_path)\n", "    \n", "    # Salvar o dataframe processado\n", "    df_final.to_csv('c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv', index=False)\n", "    print(df_final[[\"<PERSON>_Emissao\", \"<PERSON><PERSON>\", \"<PERSON><PERSON><PERSON>\", \"Valor_Final_Frete\"]].head())\n", "    print(df_final.info())"]}, {"cell_type": "code", "execution_count": null, "id": "3b0313b4", "metadata": {}, "outputs": [], "source": ["def run_seasonality_hypothesis_test(data_file):\n", "    df = pd.read_csv(data_file)\n", "    \n", "    # Preparar os dados para ANOVA por Mês\n", "    groups_month = []\n", "    for mes in sorted(df[\"Me<PERSON>\"].unique()):\n", "        groups_month.append(df[df[\"Mes\"] == mes][\"Valor_Final_Frete\"].values)\n", "\n", "    f_statistic_month, p_value_month = stats.f_oneway(*groups_month)\n", "    f_statistic_month = round(f_statistic_month, 2)\n", "\n", "    print(\"\\n--- Teste de Sazonalidade por Mês ---\")\n", "    print(f\"F-statistic (<PERSON>ê<PERSON>): {f_statistic_month}\")\n", "    print(f\"P-value (<PERSON><PERSON><PERSON>): {p_value_month}\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o Mês AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o Mês NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "    # Preparar os dados para ANOVA por Trimestre\n", "    groups_quarter = []\n", "    for trimestre in sorted(df[\"Trimestre\"].unique()):\n", "        groups_quarter.append(df[df[\"Trimestre\"] == trimestre][\"Valor_Final_Frete\"].values)\n", "\n", "    f_statistic_quarter, p_value_quarter = stats.f_oneway(*groups_quarter)\n", "    f_statistic_quarter = round(f_statistic_quarter, 2)\n", "\n", "    print(\"\\n--- Teste de Sazonalidade por Trimestre ---\")\n", "    print(f\"F-statistic (Trimestre): {f_statistic_quarter}\")\n", "    print(f\"P-value (Trimestre): {p_value_quarter}\")\n", "\n", "    if p_value_quarter < 0.05:\n", "        print(\"O p-value é menor que 0.05, o que sugere que o Trimestre AFETA significativamente o valor final do frete.\")\n", "    else:\n", "        print(\"O p-value é maior ou igual a 0.05, o que sugere que o Trimestre NÃO AFETA significativamente o valor final do frete.\")\n", "\n", "    if p_value_month < 0.05:\n", "        print(\"Conclusão: Existe evidência suficiente para rejeitar a hipótese nula.\")\n", "    else:\n", "        print(\"Conclusão: Não há evidência suficiente para rejeitar a hipótese nula.\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv'\n", "    run_seasonality_hypothesis_test(processed_data_path)"]}, {"cell_type": "code", "execution_count": null, "id": "83bd66fe", "metadata": {}, "outputs": [], "source": ["def create_seasonality_plots(data_file):\n", "    df = pd.read_csv(data_file)\n", "\n", "    # Histograma por Mês\n", "    g_month = sns.displot(\n", "        data=df,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"Me<PERSON>\",\n", "        col_wrap=4,          # 4 gráficos por linha\n", "        bins=40,\n", "        facet_kws={'sharex': False, 'sharey': False},\n", "        color=\"skyblue\"\n", "    )\n", "    g_month.set_titles(\"Mês: {col_name}\")\n", "    g_month.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.9)\n", "    g_month.fig.suptitle(\"Distribuição do Valor Final do Frete por Mês\", fontsize=16)\n", "    g_month.savefig(\"c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\sazonalidade_por_mes.png\")\n", "\n", "    # Histograma por Trimestre\n", "    g_quarter = sns.displot(\n", "        data=df,\n", "        x=\"Valor_Final_Frete\",\n", "        col=\"Trimestre\",\n", "        col_wrap=2,          # 2 gráficos por linha\n", "        bins=40,\n", "        facet_kws={'sharex': False, 'sharey': False},\n", "        color=\"lightcoral\"\n", "    )\n", "    g_quarter.set_titles(\"Trimestre: {col_name}\")\n", "    g_quarter.set_axis_labels(\"Valor Final do Frete\", \"Contagem\")\n", "    plt.subplots_adjust(top=0.85)\n", "    g_quarter.fig.suptitle(\"Distribuição do Valor Final do Frete por Trimestre\", fontsize=16)\n", "    g_quarter.savefig(\"c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\assets\\\\graficos\\\\sazonalidade_por_trimestre.png\")\n", "\n", "if __name__ == '__main__':\n", "    processed_data_path = 'c:\\\\Users\\\\<USER>\\\\OneDrive\\\\Documentos\\\\GitHub\\\\2025-2A-T19-IN03-G05\\\\notebooks\\\\Dados\\\\processed_seasonality_data.csv'\n", "    create_seasonality_plots(processed_data_path)"]}, {"cell_type": "markdown", "id": "eff776fa", "metadata": {}, "source": ["## <PERSON><PERSON><PERSON><PERSON>\n", "\n", "```A análise mostra que os valores de frete variam conforme o mês e o trimestre, indicando a existência de padrões sazonais. Isso reforça que a época do ano pode influenciar significativamente o custo do transporte.```"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}