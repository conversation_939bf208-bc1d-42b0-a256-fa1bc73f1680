import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns


def load_cte_data(filepath):
    """
    Carrega e processa dados da planilha CTE-22-23-24(in).csv

    Variáveis principais utilizadas:
    - 'Data da Emissão': data do conhecimento de transporte (formato brasileiro DD/MM/AAAA)
    - 'Valor CT-e': valor do frete em reais (formato brasileiro com vírgula decimal)
    - 'UF do início de transporte': estado de origem da carga
    - 'UF do fim do transporte': estado de destino da carga
    """
    # Carrega CSV com separador ponto-e-vírgula (padrão brasileiro)
    df = pd.read_csv(filepath, sep=";", encoding="utf-8")

    # Converte valores monetários: remove pontos (milhares) e troca vírgula por ponto (decimal)
    df["Valor CT-e"] = (
        df["Valor CT-e"]
        .astype(str)
        .str.replace(".", "")
        .str.replace(",", ".")
        .astype(float)
    )

    # Converte datas do formato brasileiro para datetime
    df["Data da Emissão"] = pd.to_datetime(df["Data da Emissão"], format="%d/%m/%Y")

    # Cria variáveis auxiliares para análises temporais
    df["Ano_Mes"] = df["Data da Emissão"].dt.to_period(
        "M"
    )  # Período ano-mês para tendência temporal
    df["Mes"] = df["Data da Emissão"].dt.month  # Mês para análise de sazonalidade

    return df


def plot_evolucao_temporal(df):
    """
    Gráfico 1: Evolução temporal dos fretes CTE (2022-2024)

    Fonte: planilha CTE-22-23-24(in).csv
    Variáveis utilizadas:
    - X: 'Ano_Mes' (período mensal derivado de 'Data da Emissão')
    - Y1: Valor médio mensal de 'Valor CT-e'
    - Y2: Contagem mensal de operações (volume)
    """
    # Agrupa dados por mês e calcula estatísticas mensais
    monthly_stats = (
        df.groupby("Ano_Mes")
        .agg(
            {
                "Valor CT-e": [
                    "mean",
                    "count",
                ]  # Média dos valores e quantidade de operações por mês
            }
        )
        .round(2)
    )

    # Configura gráfico com dois eixos Y (valor médio + volume)
    fig, ax1 = plt.subplots(figsize=(15, 8))

    # Eixo principal: mostra a evolução do valor médio mensal dos fretes
    color = "tab:blue"
    ax1.set_xlabel("Período (Ano-Mês)")
    ax1.set_ylabel("Valor Médio do Frete (R$)", color=color)
    ax1.plot(
        monthly_stats.index.astype(str),
        monthly_stats[("Valor CT-e", "mean")],
        color=color,
        linewidth=2.5,
        marker="o",
    )
    ax1.tick_params(axis="y", labelcolor=color)
    ax1.tick_params(axis="x", rotation=45)

    # Eixo secundário: mostra o volume mensal de operações
    ax2 = ax1.twinx()
    color = "tab:orange"
    ax2.set_ylabel("Número de Operações por Mês", color=color)
    ax2.plot(
        monthly_stats.index.astype(str),
        monthly_stats[("Valor CT-e", "count")],
        color=color,
        linewidth=2.5,
        marker="s",
        alpha=0.7,
    )
    ax2.tick_params(axis="y", labelcolor=color)

    plt.title(
        "Evolução Temporal dos Fretes CTE (2022-2024)\nValor Médio vs Volume de Operações",
        fontsize=14,
        fontweight="bold",
        pad=20,
    )
    ax1.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


def plot_comparacao_estados(df):
    """
    Gráfico 2: Comparação de valores médios por estado

    Fonte: planilha CTE-22-23-24(in).csv
    Variáveis utilizadas:
    - X: 'UF do início de transporte' e 'UF do fim do transporte' (estados de origem e destino)
    - Y: Média de 'Valor CT-e' agrupada por estado
    """
    # Seleciona os 8 estados com maior volume para melhor visualização
    top_estados_origem = df["UF do início de transporte"].value_counts().head(8).index
    top_estados_destino = df["UF do fim do transporte"].value_counts().head(8).index

    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))

    # Painel esquerdo: Estados de origem
    # Filtra dados pelos principais estados de origem e calcula valor médio por estado
    df_origem = df[df["UF do início de transporte"].isin(top_estados_origem)]
    origem_stats = (
        df_origem.groupby("UF do início de transporte")["Valor CT-e"]
        .mean()
        .sort_values(ascending=True)
    )

    ax1.barh(origem_stats.index, origem_stats.values, color="lightcoral", alpha=0.8)
    ax1.set_xlabel("Valor Médio do Frete (R$)")
    ax1.set_ylabel("Estado de Origem")
    ax1.set_title("Valor Médio por Estado de Origem", fontweight="bold")
    ax1.grid(axis="x", alpha=0.3)

    # Painel direito: Estados de destino
    # Filtra dados pelos principais estados de destino e calcula valor médio por estado
    df_destino = df[df["UF do fim do transporte"].isin(top_estados_destino)]
    destino_stats = (
        df_destino.groupby("UF do fim do transporte")["Valor CT-e"]
        .mean()
        .sort_values(ascending=True)
    )

    ax2.barh(destino_stats.index, destino_stats.values, color="lightblue", alpha=0.8)
    ax2.set_xlabel("Valor Médio do Frete (R$)")
    ax2.set_ylabel("Estado de Destino")
    ax2.set_title("Valor Médio por Estado de Destino", fontweight="bold")
    ax2.grid(axis="x", alpha=0.3)

    plt.tight_layout()
    plt.show()


def plot_sazonalidade_mensal(df):
    """
    Gráfico 3: Sazonalidade dos fretes ao longo do ano

    Fonte: planilha CTE-22-23-24(in).csv
    Variáveis utilizadas:
    - X: 'Mes' (mês extraído de 'Data da Emissão', de 1 a 12)
    - Y: Média de 'Valor CT-e' para cada mês do ano (todos os anos combinados)
    """
    # Calcula o valor médio dos fretes para cada mês do ano (1=Janeiro, 12=Dezembro)
    # Combina dados de todos os anos para identificar padrões sazonais
    monthly_avg = df.groupby("Mes")["Valor CT-e"].mean()

    plt.figure(figsize=(12, 6))

    # Cria gráfico de linha mostrando a variação sazonal
    plt.plot(
        monthly_avg.index,
        monthly_avg.values,
        marker="o",
        linewidth=3,
        markersize=10,
        color="darkblue",
        markerfacecolor="lightblue",
        markeredgewidth=2,
    )

    plt.xlabel("Mês do Ano")
    plt.ylabel("Valor Médio do Frete (R$)")
    plt.title(
        "Sazonalidade - Valor Médio por Mês\n(Dados combinados 2022-2024)",
        fontweight="bold",
        fontsize=14,
    )

    # Personaliza eixo X com nomes dos meses
    meses = [
        "Jan",
        "Fev",
        "Mar",
        "Abr",
        "Mai",
        "Jun",
        "Jul",
        "Ago",
        "Set",
        "Out",
        "Nov",
        "Dez",
    ]
    plt.xticks(range(1, 13), meses)
    plt.grid(True, alpha=0.3)
    plt.tight_layout()
    plt.show()


# Carregar dados da planilha CTE e gerar os três gráficos
df = load_cte_data("notebooks/Dados/CTE-22-23-24(in).csv")

# Executa as três visualizações solicitadas
plot_evolucao_temporal(df)  # Gráfico 1: Tendência temporal
plot_comparacao_estados(df)  # Gráfico 2: Comparação por estados
plot_sazonalidade_mensal(df)  # Gráfico 3: Sazonalidade mensal
