import pandas as pd
from pathlib import Path

# --- Funções auxiliares (iguais às suas) ---
def _resolve_first_existing_path(candidates, base_dir="Data"):
    base_dir = Path(base_dir)
    for candidate in candidates:
        candidate_path = base_dir / candidate
        if candidate_path.exists():
            return candidate_path
    raise FileNotFoundError(f"Nenhum dos arquivos encontrados em {base_dir}: {candidates}")

def limpar_dataset(df):
    if "Data de Emissão" in df.columns:
        df["Data de Emissão"] = pd.to_datetime(df["Data de Emissão"], errors="coerce", dayfirst=True)

    for col in df.select_dtypes(include="object"):
        if df[col].str.contains(",", regex=False).any():
            df[col] = df[col].str.replace(".", "", regex=False)
            df[col] = df[col].str.replace(",", ".", regex=False)
            try:
                df[col] = pd.to_numeric(df[col])
            except Exception:
                pass

    df = df.drop_duplicates()
    df = df.dropna(subset=["Chave de Acesso"])
    df = df.fillna(0)
    return df

def criar_features(df):
    if "Data de Emissão" in df.columns:
        df["Ano"] = df["Data de Emissão"].dt.year
        df["Mes"] = df["Data de Emissão"].dt.month
        df["Dia"] = df["Data de Emissão"].dt.day
        df["DiaSemana"] = df["Data de Emissão"].dt.dayofweek
        df["Safra"] = df["Ano"]
        df.loc[df["Mes"] < 7, "Safra"] = df["Ano"] - 1

    if "UF Origem" in df.columns:
        dummies = pd.get_dummies(df["UF Origem"], prefix="UFOrigem")
        df = pd.concat([df, dummies], axis=1)
    return df

def carregar_e_preparar(filepath):
    df = pd.read_csv(filepath, sep=";", encoding="utf-8")
    df = limpar_dataset(df)
    df = criar_features(df)
    return df

# --- Carregamento dos conjuntos ---
base_dir = Path("Data")

# Planilhas antigas (22-23-24) -> treino
cte_antigo = carregar_e_preparar(base_dir / "CTE-22-23-24(in).csv")
graneis_antigo = carregar_e_preparar(base_dir / "Graneis-22-23-24(in).csv")
train_data = pd.concat([cte_antigo, graneis_antigo], axis=0, ignore_index=True)

# Planilhas novas (24-25) -> teste
cte_novo = carregar_e_preparar(base_dir / "CTE-24-25(in).csv")
graneis_novo = carregar_e_preparar(base_dir / "Graneis-24-25(in).csv")
test_data = pd.concat([cte_novo, graneis_novo], axis=0, ignore_index=True)

print("✅ Conjuntos prontos!")
print(f"Treino: {train_data.shape}  |  Teste: {test_data.shape}")

# Amostras
print("\nAmostra treino:")
print(train_data.head())
print("\nAmostra teste:")
print(test_data.head())

def get_train_data():
    return train_data.copy()

def get_test_data():
    return test_data.copy()
