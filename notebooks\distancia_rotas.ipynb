{"cells": [{"cell_type": "code", "execution_count": 2, "id": "8e82553d", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import unicodedata"]}, {"cell_type": "code", "execution_count": 3, "id": "fdf51744", "metadata": {}, "outputs": [], "source": ["tabela_distancias = {\n", "    (\"GUAPIARA-SP\", \"MIRANTE DO PARANAPANEMA-SP\"): 529,\n", "    (\"JANDAIA-GO\", \"PEROLANDIA-GO\"): 308,\n", "    (\"JANDAIA-GO\", \"ALTO TAQUARI-MT\"): 486,\n", "    (\"JANDAIA-GO\", \"MINEIROS-GO\"): 346,\n", "    (\"JANDAIA-GO\", \"CACU-GO\"): 255,\n", "    (\"BELA VISTA-MS\", \"COSTA RICA-MS\"): 661,\n", "    (\"BELA VISTA-MS\", \"RIO BRILHANTE-MS\"): 279,\n", "    (\"BELA VISTA-MS\", \"NOVA ALVORADA DO SUL-MS\"): 323,\n", "    (\"BELA VISTA-MS\", \"ALTO TAQUARI-MT\"): 747,\n", "    (\"CASTRO-PR\", \"MIRANTE DO PARANAPANEMA-SP\"): 453,\n", "    (\"ITAU DE MINAS-MG\", \"CACU-GO\"): 670,\n", "    (\"ITAU DE MINAS-MG\", \"PEROLANDIA-GO\"): 843,\n", "    (\"UBERABA-MG\", \"MIRANTE DO PARANAPANEMA-SP\"): 561,\n", "    (\"UBERABA-MG\", \"PEROLANDIA-GO\"): 598,\n", "    (\"UBERABA-MG\", \"ALTO TAQUARI-MT\"): 716,\n", "    (\"UBERABA-MG\", \"COSTA RICA-MS\"): 636,\n", "    (\"UBERABA-MG\", \"RIO BRILHANTE-MS\"): 876,\n", "    (\"UBERABA-MG\", \"MINEIROS-GO\"): 637,\n", "    (\"UBERABA-MG\", \"CACU-GO\"): 458,\n", "    (\"UBERABA-MG\", \"NOVA ALVORADA DO SUL-MS\"): 834,\n", "}"]}, {"cell_type": "code", "execution_count": 4, "id": "45758e44", "metadata": {}, "outputs": [], "source": ["df = pd.read_csv(\"Dados/CTE-22-23-24(in).csv\", sep=\";\", encoding=\"utf-8\")\n", "\n", "\n", "def normalizar_texto(texto):\n", "    texto_sem_acento = unicodedata.normalize(\"NFD\", str(texto))\n", "    texto_sem_acento = \"\".join(\n", "        char for char in texto_sem_acento if unicodedata.category(char) != \"Mn\"\n", "    )\n", "    return texto_sem_acento.strip().upper()\n", "\n", "\n", "def buscar_distancia(cidade_inicio, uf_inicio, cidade_fim, uf_fim):\n", "    origem = f\"{normalizar_texto(cidade_inicio)}-{normalizar_texto(uf_inicio)}\"\n", "    destino = f\"{normalizar_texto(cidade_fim)}-{normalizar_texto(uf_fim)}\"\n", "    return tabela_distancias.get((origem, destino), None)\n", "\n", "\n", "df[\"distancia_rota\"] = df.apply(\n", "    lambda row: buscar_distancia(\n", "        row[\"Cidade de início de transporte\"],\n", "        row[\"UF do início de transporte\"],\n", "        row[\"Cidade de fim do transporte\"],\n", "        row[\"UF do fim do transporte\"],\n", "    ),\n", "    axis=1,\n", ")"]}, {"cell_type": "code", "execution_count": 6, "id": "d048405c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Chave de Acesso Empresa Data da Emissão             Fornecedor  \\\n", "0     3,52206E+43    A006      23/06/2022  TE LOG LOGISTICA LTDA   \n", "1     3,52403E+43    A006      13/03/2024   G10 TRANSPORTES S.A.   \n", "2     3,52403E+43    A006      15/03/2024      DECOLI CARGO LTDA   \n", "3     3,52403E+43    A006      16/03/2024      DECOLI CARGO LTDA   \n", "4     3,52403E+43    A006      12/03/2024    LOGUM LOGISTICA S A   \n", "5     3,52403E+43    A006      13/03/2024    LOGUM LOGISTICA S A   \n", "6     3,52403E+43    A006      16/03/2024    LOGUM LOGISTICA S A   \n", "7     3,52403E+43    A006      18/03/2024    LOGUM LOGISTICA S A   \n", "8     3,52403E+43    A006      19/03/2024    LOGUM LOGISTICA S A   \n", "9     3,52403E+43    A006      21/03/2024    LOGUM LOGISTICA S A   \n", "\n", "   Código Fornecedor Cidade de fim do transporte UF do fim do transporte  \\\n", "0          2001407.0     MIRANTE DO PARANAPANEMA                      SP   \n", "1          2000207.0              Ribeirao Preto                      SP   \n", "2          2001733.0     Mirante do Paranapanema                      SP   \n", "3          2001733.0     Mirante do Paranapanema                      SP   \n", "4          1005678.0             Duque de Caxias                      RJ   \n", "5          1005678.0          SAO CAETANO DO SUL                      SP   \n", "6          1005678.0          SAO CAETANO DO SUL                      SP   \n", "7          1005678.0                     BARUERI                      SP   \n", "8          1005678.0          SAO CAETANO DO SUL                      SP   \n", "9          1005678.0          SAO CAETANO DO SUL                      SP   \n", "\n", "  Cidade de início de transporte UF do início de transporte  \\\n", "0                     PIRACICABA                         SP   \n", "1        Mirante do Paranapanema                         SP   \n", "2           Nova Alvorada do Sul                         SP   \n", "3                    Sertaozinho                         SP   \n", "4                       Paulinia                         SP   \n", "5                       Paulinia                         SP   \n", "6                       Paulinia                         SP   \n", "7                       Paulinia                         SP   \n", "8                       Paulinia                         SP   \n", "9                       Paulinia                         SP   \n", "\n", "                                  Produto  distancia_rota  \n", "0            PRODUTO QUIMICO CLASSIFICADO             NaN  \n", "1                        ETANOL HIDRATADO             NaN  \n", "2  REDUTOR PLANET RPS 3 280 F TGM 4801537             NaN  \n", "3            BRACO DEDINI 17.006.02.01 R0             NaN  \n", "4                     ETANOL HIDRATADO GR             NaN  \n", "5                     ETANOL HIDRATADO GR             NaN  \n", "6                     ETANOL HIDRATADO GR             NaN  \n", "7                     ETANOL HIDRATADO GR             NaN  \n", "8                     ETANOL HIDRATADO GR             NaN  \n", "9                     ETANOL HIDRATADO GR             NaN  \n"]}], "source": ["print(df.head(10))"]}, {"cell_type": "code", "execution_count": null, "id": "686ffbe7", "metadata": {}, "outputs": [], "source": ["# Salva a nova coluna no CSV\n", "df.to_csv(\n", "    \"Dados/CTE-22-23-24_com_distancias.csv\", sep=\";\", index=False, encoding=\"utf-8\"\n", ")"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}