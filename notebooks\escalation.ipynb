{"cells": [{"cell_type": "code", "execution_count": 80, "id": "403d9a64", "metadata": {}, "outputs": [], "source": ["import os\n", "import sys\n", "import numpy as np\n", "import pandas as pd\n", "from pathlib import Path\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler\n", "from sklearn.model_selection import train_test_split\n", "import matplotlib.pyplot as plt\n", "\n", "# Configurações do matplotlib\n", "plt.style.use('default')\n", "%matplotlib inline\n", "\n", "# Garante que o diretório \"notebooks\" esteja no caminho do Python\n", "sys.path.append(\".\")\n", "\n", "import loader"]}, {"cell_type": "code", "execution_count": 81, "id": "ff153e2d", "metadata": {}, "outputs": [], "source": ["def carregar_dados():\n", "    \"\"\"Carrega train_data e test_data do explorationloader\"\"\"\n", "    try:\n", "        train_data = loader.train_data.copy()\n", "        test_data = loader.test_data.copy()\n", "        print(f\"✅ <PERSON><PERSON> carregados: Tre<PERSON>={len(train_data)}, Teste={len(test_data)}\")\n", "        return train_data, test_data\n", "    except Exception as e:\n", "        print(f\"❌ Erro ao importar explorationloader: {e}\")\n", "        return None, None"]}, {"cell_type": "code", "execution_count": 82, "id": "58fbd0f9", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["INICIANDO PROCESSO DE ESCALONAMENTO\n", "==================================================\n", "\n", " 1. Carregando dados...\n", "✅ Dados carregados: T<PERSON><PERSON>=145889, <PERSON><PERSON>=71645\n", "✅ Dados carregados com sucesso!\n", "Colunas disponíveis: ['<PERSON><PERSON>', '<PERSON><PERSON><PERSON><PERSON>', 'Data da Emissão', 'Fornecedor', 'Código Fornecedor', 'Cidade de fim do transporte', 'UF do fim do transporte', 'Cidade de início de transporte', 'UF do início de transporte', 'Produto', 'Valor Total da Prestação do Serviço', 'Valor do ICMS', 'Total Imposto', 'Valor total da carga', 'Tipo de Medida 01', 'Quantidade de Carga 01']\n"]}], "source": ["print(\"INICIANDO PROCESSO DE ESCALONAMENTO\")\n", "print(\"=\" * 50)\n", "\n", "# Carrega os dados\n", "print(\"\\n 1. Carregando dados...\")\n", "train_data, test_data = carregar_dados()\n", "\n", "if train_data is None or test_data is None:\n", "    print(\"❌ Não foi possível carregar os dados. Verifique a pasta 'Data'.\")\n", "    raise Exception(\"Erro no carregamento dos dados\")\n", "\n", "print(\"✅ Dados carregados com sucesso!\")\n", "print(f\"Colunas disponíveis: {list(train_data.columns)}\")"]}, {"cell_type": "code", "execution_count": 83, "id": "dcba2153", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🔧 2. <PERSON><PERSON><PERSON><PERSON> var<PERSON>...\n", " Colunas encontradas: ['Valor Total da Prestação do Serviço', 'Valor total da carga', 'Quantidade de Carga 01']\n", " Colunas não encontradas: []\n"]}], "source": ["print(\"\\n🔧 2. De<PERSON><PERSON><PERSON> variáveis...\")\n", "V1 = \"Valor Total da Prestação do Serviço\"  # Robust\n", "V2 = \"Valor total da carga\"                 # Standard\n", "V3 = \"Quantidade de Carga 01\"               # MinMax\n", "\n", "cols = [V1, V2, V3]\n", "\n", "# Combina treino e teste para estatísticas do \"conjunto completo\"\n", "df_full = pd.concat([train_data, test_data], axis=0, ignore_index=True)\n", "\n", "# Verifica quais colunas existem nos dados\n", "colunas_existentes = [col for col in cols if col in df_full.columns]\n", "print(f\" Colunas encontradas: {colunas_existentes}\")\n", "print(f\" Colunas não encontradas: {[col for col in cols if col not in df_full.columns]}\")"]}, {"cell_type": "code", "execution_count": 84, "id": "829cfbe8", "metadata": {}, "outputs": [], "source": ["if len(colunas_existentes) < 3:\n", "    print(\"\\n🔍 Procurando colunas alternativas...\")\n", "    \n", "    # Mapeia colunas alternativas\n", "    alternativas = {\n", "        V1: [\"Valor CT-e\", \"Valor do CT-e\", \"Valor do Frete\"],\n", "        V2: [\"Valor total da carga\", \"Valor da Carga\"],\n", "        V3: [\"Quantidade de Carga 01\", \"Quantidade\", \"Qtd\"]\n", "    }\n", "    \n", "    # Procura por alternativas\n", "    for col_original, alternativas_list in alternativas.items():\n", "        if col_original not in colunas_existentes:\n", "            for alt in alternativas_list:\n", "                if alt in df_full.columns:\n", "                    print(f\"  ✅ {col_original} -> {alt}\")\n", "                    # Substitui na lista de colunas\n", "                    idx = cols.index(col_original)\n", "                    cols[idx] = alt\n", "                    colunas_existentes.append(alt)\n", "                    break\n", "    \n", "    print(f\"\\n📋 Colunas finais para análise: {cols}\")"]}, {"cell_type": "code", "execution_count": 85, "id": "2674d18f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "🧹 3. Preparando dados...\n", " Dimensão final do dataframe: (20150, 17)\n", "\n", "Primeiras linhas dos dados:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Valor Total da Prestação do Serviço</th>\n", "      <th>Valor total da carga</th>\n", "      <th>Quantidade de Carga 01</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5874.00</td>\n", "      <td>2860.82</td>\n", "      <td>35600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5844.30</td>\n", "      <td>2846.35</td>\n", "      <td>35420.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7825.95</td>\n", "      <td>3811.48</td>\n", "      <td>47430.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7882.05</td>\n", "      <td>4968.09</td>\n", "      <td>47770.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7758.30</td>\n", "      <td>3778.52</td>\n", "      <td>47020.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Valor Total da Prestação do Serviço  Valor total da carga  \\\n", "0                              5874.00               2860.82   \n", "1                              5844.30               2846.35   \n", "2                              7825.95               3811.48   \n", "3                              7882.05               4968.09   \n", "4                              7758.30               3778.52   \n", "\n", "   Quantidade de Carga 01  \n", "0                 35600.0  \n", "1                 35420.0  \n", "2                 47430.0  \n", "3                 47770.0  \n", "4                 47020.0  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"\\n🧹 3. Preparando dados...\")\n", "\n", "# Garante que as colunas são numéricas\n", "for c in cols:\n", "    if c in df_full.columns:\n", "        df_full[c] = pd.to_numeric(df_full[c], errors=\"coerce\")\n", "\n", "# Remove linhas totalmente nulas nas colunas selecionadas\n", "df_full = df_full.dropna(subset=cols, how=\"any\").reset_index(drop=True)\n", "\n", "print(f\" Dimensão final do dataframe: {df_full.shape}\")\n", "print(\"\\nPrimeiras linhas dos dados:\")\n", "display(df_full[cols].head())"]}, {"cell_type": "code", "execution_count": 86, "id": "775f331f", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "📊 4. Calculando estatísticas descritivas...\n", "\n", "Estatísticas descritivas:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Variável</th>\n", "      <th>min</th>\n", "      <th>q1</th>\n", "      <th>mediana</th>\n", "      <th>q3</th>\n", "      <th>max</th>\n", "      <th>media</th>\n", "      <th>sigma_pop</th>\n", "      <th>iqr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>Valor Total da Prestação do Serviço</td>\n", "      <td>0.00</td>\n", "      <td>4298.455</td>\n", "      <td>5635.34</td>\n", "      <td>7630.110</td>\n", "      <td>15886.36</td>\n", "      <td>6018.1201</td>\n", "      <td>2306.0508</td>\n", "      <td>3331.655</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>Valor total da carga</td>\n", "      <td>0.00</td>\n", "      <td>3438.170</td>\n", "      <td>4051.95</td>\n", "      <td>6168.175</td>\n", "      <td>509203.98</td>\n", "      <td>6992.7912</td>\n", "      <td>13781.9256</td>\n", "      <td>2730.005</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>Quantidade de Carga 01</td>\n", "      <td>39.06</td>\n", "      <td>37380.000</td>\n", "      <td>47210.00</td>\n", "      <td>48810.000</td>\n", "      <td>74380.00</td>\n", "      <td>42777.1644</td>\n", "      <td>9181.9824</td>\n", "      <td>11430.000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                              Variável    min         q1   mediana         q3  \\\n", "0  Valor Total da Prestação do Serviço   0.00   4298.455   5635.34   7630.110   \n", "1                 Valor total da carga   0.00   3438.170   4051.95   6168.175   \n", "2               Quantidade de Carga 01  39.06  37380.000  47210.00  48810.000   \n", "\n", "         max       media   sigma_pop        iqr  \n", "0   15886.36   6018.1201   2306.0508   3331.655  \n", "1  509203.98   6992.7912  13781.9256   2730.005  \n", "2   74380.00  42777.1644   9181.9824  11430.000  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"\\n📊 4. Calculando estatísticas descritivas...\")\n", "\n", "def pop_std(x: pd.Series) -> float:\n", "    \"\"\"Calcula desvio padrão populacional\"\"\"\n", "    return float(np.std(x.values, ddof=0))\n", "\n", "stats = []\n", "for c in cols:\n", "    if c in df_full.columns:\n", "        s = df_full[c].astype(float)\n", "        stats.append({\n", "            \"Variável\": c,\n", "            \"min\": float(s.min()),\n", "            \"q1\": float(s.quantile(0.25)),\n", "            \"mediana\": float(s.median()),\n", "            \"q3\": float(s.quantile(0.75)),\n", "            \"max\": float(s.max()),\n", "            \"media\": float(s.mean()),\n", "            \"sigma_pop\": pop_std(s),\n", "            \"iqr\": float(s.quantile(0.75) - s.quantile(0.25)),\n", "        })\n", "\n", "stats_df = pd.DataFrame(stats, columns=[\"Variável\",\"min\",\"q1\",\"mediana\",\"q3\",\"max\",\"media\",\"sigma_pop\",\"iqr\"])\n", "print(\"\\nEstatísticas descritivas:\")\n", "display(stats_df.round(4))"]}, {"cell_type": "code", "execution_count": 87, "id": "9db04384", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "5. Aplicando escalonadores...\n", "Escalonamento concluído!\n"]}], "source": ["print(\"\\n5. Aplicando escalonadores...\")\n", "\n", "df_scaled = df_full[cols].copy()\n", "\n", "# Robust para V1 (primeira coluna)\n", "S1 = stats_df.loc[stats_df[\"Variável\"]==cols[0]].iloc[0]\n", "iqr = S1[\"iqr\"] if S1[\"iqr\"] != 0 else 1.0 \n", "df_scaled[cols[0]] = (df_full[cols[0]] - S1[\"mediana\"]) / iqr\n", "\n", "# Standard para V2 (segunda coluna)\n", "S2 = stats_df.loc[stats_df[\"Variável\"]==cols[1]].iloc[0]\n", "sigma = S2[\"sigma_pop\"] if S2[\"sigma_pop\"] != 0 else 1.0\n", "df_scaled[cols[1]] = (df_full[cols[1]] - S2[\"media\"]) / sigma\n", "\n", "# MinMax para V3 (terc<PERSON> colu<PERSON>)\n", "S3 = stats_df.loc[stats_df[\"Variável\"]==cols[2]].iloc[0]\n", "den = (S3[\"max\"] - S3[\"min\"]) if (S3[\"max\"] - S3[\"min\"]) != 0 else 1.0\n", "df_scaled[cols[2]] = (df_full[cols[2]] - S3[\"min\"]) / den\n", "\n", "print(\"Escalonamento concluído!\")"]}, {"cell_type": "code", "execution_count": 88, "id": "cb462fa3", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "6. <PERSON><PERSON><PERSON> comparativas (10 primeiras linhas)\n", "\n", "Dados Originais:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Valor Total da Prestação do Serviço</th>\n", "      <th>Valor total da carga</th>\n", "      <th>Quantidade de Carga 01</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>5874.00</td>\n", "      <td>2860.82</td>\n", "      <td>35600.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5844.30</td>\n", "      <td>2846.35</td>\n", "      <td>35420.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>7825.95</td>\n", "      <td>3811.48</td>\n", "      <td>47430.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>7882.05</td>\n", "      <td>4968.09</td>\n", "      <td>47770.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7758.30</td>\n", "      <td>3778.52</td>\n", "      <td>47020.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>7825.95</td>\n", "      <td>3978.58</td>\n", "      <td>47430.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>7962.90</td>\n", "      <td>4048.20</td>\n", "      <td>48260.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>7731.90</td>\n", "      <td>5266.60</td>\n", "      <td>46860.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>7768.20</td>\n", "      <td>5291.32</td>\n", "      <td>47080.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>7728.60</td>\n", "      <td>5264.34</td>\n", "      <td>46840.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Valor Total da Prestação do Serviço  Valor total da carga  \\\n", "0                              5874.00               2860.82   \n", "1                              5844.30               2846.35   \n", "2                              7825.95               3811.48   \n", "3                              7882.05               4968.09   \n", "4                              7758.30               3778.52   \n", "5                              7825.95               3978.58   \n", "6                              7962.90               4048.20   \n", "7                              7731.90               5266.60   \n", "8                              7768.20               5291.32   \n", "9                              7728.60               5264.34   \n", "\n", "   Quantidade de Carga 01  \n", "0                 35600.0  \n", "1                 35420.0  \n", "2                 47430.0  \n", "3                 47770.0  \n", "4                 47020.0  \n", "5                 47430.0  \n", "6                 48260.0  \n", "7                 46860.0  \n", "8                 47080.0  \n", "9                 46840.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Dados Escalonados:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Valor Total da Prestação do Serviço</th>\n", "      <th>Valor total da carga</th>\n", "      <th>Quantidade de Carga 01</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>0.071634</td>\n", "      <td>-0.299811</td>\n", "      <td>0.478349</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>0.062720</td>\n", "      <td>-0.300861</td>\n", "      <td>0.475928</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>0.657514</td>\n", "      <td>-0.230832</td>\n", "      <td>0.637481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>0.674353</td>\n", "      <td>-0.146910</td>\n", "      <td>0.642055</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>0.637209</td>\n", "      <td>-0.233224</td>\n", "      <td>0.631966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>0.657514</td>\n", "      <td>-0.218708</td>\n", "      <td>0.637481</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>0.698620</td>\n", "      <td>-0.213656</td>\n", "      <td>0.648646</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>0.629285</td>\n", "      <td>-0.125250</td>\n", "      <td>0.629814</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>0.640180</td>\n", "      <td>-0.123457</td>\n", "      <td>0.632773</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>0.628294</td>\n", "      <td>-0.125414</td>\n", "      <td>0.629545</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Valor Total da Prestação do Serviço  Valor total da carga  \\\n", "0                             0.071634             -0.299811   \n", "1                             0.062720             -0.300861   \n", "2                             0.657514             -0.230832   \n", "3                             0.674353             -0.146910   \n", "4                             0.637209             -0.233224   \n", "5                             0.657514             -0.218708   \n", "6                             0.698620             -0.213656   \n", "7                             0.629285             -0.125250   \n", "8                             0.640180             -0.123457   \n", "9                             0.628294             -0.125414   \n", "\n", "   Quantidade de Carga 01  \n", "0                0.478349  \n", "1                0.475928  \n", "2                0.637481  \n", "3                0.642055  \n", "4                0.631966  \n", "5                0.637481  \n", "6                0.648646  \n", "7                0.629814  \n", "8                0.632773  \n", "9                0.629545  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["print(\"\\n6. Tabelas comparativas (10 primeiras linhas)\")\n", "\n", "head_orig = df_full[cols].head(10)\n", "head_scaled = df_scaled[cols].head(10)\n", "\n", "print(\"\\nDados Originais:\")\n", "display(head_orig)\n", "\n", "print(\"\\nDados Escalonados:\")\n", "display(head_scaled)"]}, {"cell_type": "code", "execution_count": 89, "id": "3b6ade47", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "7. <PERSON><PERSON><PERSON> histogram<PERSON>...\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1500x1500 with 6 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Gráfico combinado salvo: histogramas_comparacao_completa.png\n"]}], "source": ["print(\"\\n7. Gerando histogramas...\")\n", "\n", "# Cria diretório para salvar os gráficos\n", "outdir = Path(\"assets/escalonamento\")\n", "outdir.mkdir(parents=True, exist_ok=True)\n", "\n", "# Gera histogramas para cada variável\n", "fig, axes = plt.subplots(len(cols), 2, figsize=(15, 5*len(cols)))\n", "fig.suptitle('Comparação dos Dados: <PERSON><PERSON> vs Depois do Escalonamento', fontsize=16, fontweight='bold')\n", "\n", "for i, c in enumerate(cols):\n", "    # An<PERSON> (dados originais)\n", "    axes[i, 0].hist(df_full[c], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    axes[i, 0].set_title(f\"Antes — {c}\", fontsize=12, fontweight='bold')\n", "    axes[i, 0].set_xlabel(c, fontsize=10)\n", "    axes[i, 0].set_ylabel(\"Frequência\", fontsize=10)\n", "    axes[i, 0].grid(True, alpha=0.3)\n", "    \n", "    # Depois (dados escalonados)\n", "    axes[i, 1].hist(df_scaled[c], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    axes[i, 1].set_title(f\"Depois — {c} (escalonado)\", fontsize=12, fontweight='bold')\n", "    axes[i, 1].set_xlabel(f\"{c} (escalonado)\", fontsize=10)\n", "    axes[i, 1].set_ylabel(\"Frequência\", fontsize=10)\n", "    axes[i, 1].grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Salva a figura combinada\n", "combined_filename = \"histogramas_comparacao_completa.png\"\n", "fig.savefig(outdir / combined_filename, dpi=300, bbox_inches='tight')\n", "print(f\"Gráfico combinado salvo: {combined_filename}\")"]}, {"cell_type": "code", "execution_count": 90, "id": "ce258d85", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_before_Valor_Total_da_Prestação_do_Serviço.png\n"]}, {"data": {"image/png": "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*****************************************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_after_Valor_Total_da_Prestação_do_Serviço.png\n"]}, {"data": {"image/png": "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*****************************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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_before_Valor_total_da_carga.png\n"]}, {"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAAA90AAAJOCAYAAACqS2TfAAAAOnRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjEwLjUsIGh0dHBzOi8vbWF0cGxvdGxpYi5vcmcvWftoOwAAAAlwSFlzAAAPYQAAD2EBqD+naQAAXWxJREFUeJzt3Qe4lNW5P+xFByMgShNFRI1ir4nBWFCJaDzGdkxiicYajSa2qEdjNycau7ERY0uixpLYJTasUezYo8eCYlQEG0UpAvNdz/pn5pu92TThZXa57+sa955517xlZu2R36zWqlQqlRIAAACw0LVe+LsEAAAAgtANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AzcQ777yTWrVqVbk99NBDtT6lJilet+rXMV7XBfXTn/60sr9BgwYtlPMEoGkQugFamDXWWKNOoFh66aXT9OnTF/pxqo9x9dVXp5bowAMPrLwGbdu2TR9++OFsy26++eaVsj179kxfffVVag6KCLBlUa+q9w0AjZHQDdCCPP300+mVV16p89iYMWPS3XffXbNzas6idbNsxowZ6brrrmuw3OjRo9PDDz9cub/77rundu3aLZJzBACKJXQDtCCza3FuqS3RRfvOd76TBgwYULn/5z//ucFy11xzTSqVSg2G9cZswoQJtT4FGiH1AqAuoRughZg6dWr661//Wrm/8sorV36/44470ieffDLXrsFvv/12uuSSS9Jaa62VOnbsmLtB77fffumzzz6rPCfGq9bv6rv33ntX9rH88svX2fbRRx+l4447Lq2zzjqpc+fOeb8rrbRSOvjgg3MLcH1ffPFFOvXUU9N6662Xy0eLcJxHPH///fdvdK321QH6xRdfzLf6/vKXv1R+X3fdddPaa6+du/yfcMIJ6fvf/35accUV0xJLLJGvdamllkqbbLJJuvDCC79WF/S///3vadttt029e/dO7du3T926dUsbbbRROuecc9KXX34512ECt912Wy6/+OKLp+WWW26Ox4rnRLf5av3796/sr/6XC88++2zac889c5moB3GMGA5x5JFHpn//+9+zjF2PejW7cz355JPzY59++mk6+uij05ZbbpnrXtSZuO5evXql733ve/m1r/7CY0E98sgj+W/gG9/4RlpyySXTLrvskt566605PufKK69MP/zhD9Oqq66aunfvnt/nLl265Dp9zDHHpI8//ni+zyNer3hu1KfYV7ye8X7tsMMO6b777quUGzVqVDrssMNynerbt28+7w4dOqRlllkmbbfddvmzYW7d+qPe/PrXv04rrLBCPvcTTzyxUvall17K+4lziNs222yTnn/++fz+zO4z4ZZbbkk/+clP8udMvE/xfkVdWG211dIhhxyyUIcoACwSJQBahBtuuCGSReU2YsSIUrt27Sr3f//738/ynAcffLDOczbeeOM698u3TTfdtPKczTbbrMEy5Vu/fv0qZR9//PFS9+7dZ1u2a9eupUceeaTOOQ0aNGiO+//Rj35Uakzef//9Ups2bSrn96tf/arO9qeffrrO+V9wwQX58YkTJ87xOuM2ePDg0vTp0yv7GjVqVJ3t8f6VRbkf/vCHc9zfqquuWvrggw/qnF/19k022WSW92dO5nb+e+21V6XseeedV2rduvUc60L5eupfZ0O3k046KZd96aWX5lp27733nmO9j+PNizvuuKPUtm3bWfa/5JJLlgYOHFi5H38j1dZff/05nt8yyyyT69G8uuuuu0qdO3ee7f4OPfTQOuc8t9fnlFNOqbP/q666ao71orz/qNuLL774LPvr2LFj6Xvf+16Dnwlh5513nuP5dOnSpfTiiy/O8+sBUGttF020B6DWqruQRytxdH0ePHhw+sc//lHZ/otf/GKO+/jnP/+ZWwyjpfPWW2/NrVjl1r0nnngi7/Oggw5K//Vf/5WOOuqoyvN+9KMfpQ022CD/3rVr10oX1Gh1K7fi9evXL5fr1KlT+tvf/pbHno8fPz7tvPPO6Y033sjP+9e//lWZkbt169a5VTRa7GMf0WLXGGfr7tOnT25RLbfAx7juM844I7Vp02aWLufRSrjbbrvl36MFMFoO4zWNVsdokY6W7ddeey3ddNNNuSX8/vvvzy3X0Uo6N7/97W/TjTfeWLkf+91qq63yaxr7C/F7jCd/4IEHGtzHo48+mltif/zjH+cW9/rzA9R31lln5VbeoUOHVh6LXg1xLSFascv154gjjqi0OEeL7K677pomTZqUrrrqqtySWq4Lb775Zm5Bjn0/88wz6YYbbqhzvLKoo+V6Ei3I3/72t3PrfvQYmDJlSho5cmRuxY1jxjFi0rso83XFOe67776VSQnjvdxnn33ytcbwgREjRsz2udFTI1qDo0dDXFvUjffffz9fW/RAid9/85vf5F4mc/Puu+/m1vVyr4WoRz/4wQ9yq/m4ceNmeW9jgr/YFn+fPXr0yK3R0ZvkscceSw8++GAuc9ppp+Vri3o4u3qx4YYb5noez433L17XuP54D8viPY06HfWwurW9vniPom7G+xavX7R0R4+YaAGP3i/x2RGt+MOGDZvr6wHQKNQ69QNQvGi9rG5tPeuss/Ljf/7zn+u0INVvParf4rfjjjuWZs6cmbd98skndfZZv6W8+nnRMlZftOiWt3fr1i3vr2zSpEmlHj16zNL6+9xzz9VplS2fS3Vr7jvvvFNq7L0M7rnnnvz4V199Vec64/Wt76OPPirddtttpUsuuaR09tln5/dujTXWqDxnn332mWtL94wZM3Jra/nxaHWtbiE/+uij6zxv5MiRlW31Wxjffffd+br2eWk13n777Svbo4U2rrls2LBhdZ4fLeKza3Gdkzjvv/3tb6WLLrqo8jpGC3L5uaeeeup8nXN9f/3rX+s85/LLL69si+dX9yqp39Idvvjii9L9999fuuyyy0rnnntuPr/q12WFFVYozYsjjjiiznlce+21dbZHXWjoel5//fXS9ddfX7rwwgsrr89iiy1W2U98Vszudd9pp53yfqtFT5rqMsccc0xl26effpr/5mfX0h2mTZuWe7lcccUV+T2P84keCeXndOjQIZcBaAq0dAO0ADFuNWbPLrd8RYtyiJbmGOsZLX8hWvzOPffc2e4nWrHL47WjRS5aPaMFKlSP654X0ZJWFs+NltPZefzxx9Mvf/nL3PIV5aL1L1plY+x3jFmN1u4Y/xkt99FiPi/OPvvstKBifOrqq68+13Lbb799brErv0bxfkRLXrR+R+tjWfUY5cmTJ6ef//znuSV85syZs9139Vjn2Xn99dfz2OayPfbYo9LSHvbaa6905plnVu5Hq2y0ftYXPQvmNo7766huBd56661zy2/1axwtsOXXKcrGGOR5FXUlru+uu+6aY7l5eR3nJFrdq5V7LIQYs7zxxhtXWo7ri7+5k046qU6r8Nc9v+iNUhZ/L9XnUW75rx5DHeOjo3dD/I3NyZyOH70XYr9zej2i7pTF30L8TcxuAsdrr702v8dzGssec1TE9ljyEKCxE7oBWoDqf9xGt9uYMCnEpFIxqVZ0US7/YzfCV3Q5bUj9CY9iwqWyOQXDhlSHwLkpB674giC6pkY4jW6mMbFb3MqiG+rpp5+euyrPTXX3968rvnSYl9Adr1N0yb700kvz/egmGwGrumt5TBgVAbPs2GOPnadZ5SN8zO9rHcea0/3ZfYFSPRP7wlR9fvXPpfxYuQ7M75c70S16boF7Xl/HOfn8888rv8ffVQyTqNbQdYUYphETxc3NtGnT5vu1jAnp5ia+eHvhhRcW6PVpqF5Uvx4huvbP6X7Zc889lwP6vHyeLOh7BrComL0coJl78sknc6twdQtz9czD5cAdxo4dO8dxkvXXjq4/S/n8iJbysmitivG4s7sdcMABlbJbbLFFHr8da47/8Y9/zGM7Y+blcjCJMB3jfhub6pm6Y9xrzFhdPTN0tDZWf9lRPVZ5zTXXTC+//HIe0x09vmPM7td9rUO5d8Ls7pfHXNcXM1sXofr86p9L/cdmd24Nidf5zjvvrNyP+QhijHmMu47X8Vvf+lZaWGIcctnEiRNzT4VqDV1X/fc5Zui+995783Pj/C6++OIFei3j72RuPSCqA3e0ikeLdgTeOH70MJgXDdWL6tej/NlSbcyYMQ3uK+YXKAfu+HyJFRfiC6o4n3n58gSgMRK6AZq5+V2De2Gt2V0dIBtaiqo80VWIVszobv2rX/2qzi1aAKObc3mCq+gGH18gRFfWmPgpliuLSckefvjhygRt8Q/2eWm5i3/EL+htftbTjmuIJY+qW7LL3fpD/eWvqpdwi2W3okU9XtN4reZ3wrhVVlmlThiLib3Kww3Cn/70p9m+Nwuq/hc1c6sL0eW+OqDFRH/VXfCry85t3zH5WvV1Rq+OmMgrutZH4Gxo+bavqzxRYFlMmFfdhbu62/fs3uc4t5iMLHp0RD2OCQXnV3RjL4u/leuvv77O9qi35aX46i8T+N///d95srQIu1HHql/3BX09qpcrjN4KsfRcQ6rPKf6mY5LAcqivnggQoCnRvRygGYtQV/2P7uhu2tAMzTEL+auvvpp/j5bBGCsZXacXRPzjPWZSDrEGdPxjOrrcxhjsaHGMwBozMsexouXxu9/9bm7BjXHa0W00QlH8wz9aCGMsbJx7dFmN4BoBNK4jZgaPfUagiYA1u1a2xiKuOdaMrh8Q119//cpM3tVBOVq3Q7ToxxcNiy22WB4PPr9hKJ57+OGH53W/y+OiI5zFFx0xG3p1mImAH+uELyz1Z7yO9deHDBmSv0CIWbVjPH6cW4SwCITRShwt0NHqGi2c0SOgLL44iPHZs9t3PCdCeVxvrPMcY8OjLpS7Okd9i0Af9S32uzC7J8e1VI89j/kPojdGefby2a2pHu9zeSbv+BIgZviOsdjxZUOsCDC/Yu6DGMZQbmmP1yRa0+PLqwi78TcV64iff/75+W8tXqtyy/Khhx6a19COv9WY32FBxOz40UOjvMJBzIAeLe8xJ0DUt9kNE4jXoyzet/iiJN7T+BuPXgAATVKtZ3IDoDj1Z1S+5pprGiw3fPjwOuXOP//8eZrFOWYdrr8uctnhhx/e4Bq7Bx98cKXMY489Nsd1uuvPwv3hhx/Otey3v/3tPCt4YxTnXz3je/kWM0bP7b0r35Zeeuk6axxXz4Q9t3W6d9lllzm+djEjfP31oOc2C/28WHfddRs83k033fS11ukumzJlSn49Giofa0SHM844o8HtMQN89frY1WuGf911umOW+Ybe35iRfb311mvwPXvjjTcaXFM71vvefffd53l29q+7TveBBx7YYJktt9yyzuzu1X/f8zpr/OzW6Y6Zx7fYYovK/f79+1eeE6sY9OnTp8Fzivfo67wvALWmezlAM1bdVTy6au60004NlovWzepJ0hZGF/P//d//zS1nyy67bJ2ZsqtFC1as9RwtsNHaG2sER9lonYz7hxxySG4F3HTTTXP5aDW86KKLcmtgtHiX1zSO50V31mhNGz58+Gwngqu1mDwqZueuFpO/1Z9hOsTEa9EiGK3O0Y06Zm2PWeej9TNa+OdXvE6xvxgz+/3vfz+3AsfrFPUi1liOsfPRMvt19j03N998c9pxxx3z+zW7eQBituqYfyBaqGMG+nhdohdDtPpGS3i0mEYLbf0J6mIOgmixjzrQkBjzH2Ojo0U9Xsd4D/bff/88JCHGUC9M0doda6dHfY1zj3ocs3THdUWrb0OitTnWKY9riJ4McU6bbbZZrscxG//XEe9v/F3F/AYxq3/sM6493ttoOY7tZRdeeGE69dRT82seZaIlOp4X8w0s6N9R/E3GrOhxzDiHuEUvl7jeb37zmw32TIk6Eq3a8VkV72m8jtHzIerQ/AznAGhMWkXyrvVJAADQvMTEhhHc6y8nFsMGYjhFefhJfAly2WWX1egsAYrXOJsCAABo0mKeiGj9j5n5o2dK9FSJSeWGDh1aCdwRyGOcP0BzJnQDAFCI9957L68w0JAYQhCTvi3MifsAGiOhGwCAha5v3755PH7MmB7LlMUKA7EcWqxEEOPzf/7zn6cBAwbU+jQBCmdMNwAAABTE7OUAAABQEKEbAAAACmJMd4FmzpyZPvjgg9S5c+fZrksKAABA0xMjtSdOnJj69Okzy/KI1YTuAkXgjklEAAAAaL4rNSy77LKz3S50FyhauMtvQpcuXVJjbIkfN25c6tGjxxy/mYEiqH/UirpHrah71Iq6Ry3NbMb1b8KECbmRtZz7ZkfoLlC5S3kE7sYauqdMmZLPrbn9AdD4qX/UirpHrah71Iq6Ry3NbAH1b25DiZvnVQMAAEAjIHQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFaVvUjmkaxo8fnyZNmpRatWpV+LG6dOmSevToUfhxAAAAGguhuwX7+OOP0+8vuiS9+uZbqVQqFX68zp06pisvGyp4AwAALYbQ3YJNmDAhTZ42LW22x8/SUkv3LfRYn3z47/TQXy7NxxS6AQCAlkLoJi3Ve9nUu1//Wp8GAABAs2MiNQAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAtJXQ/8sgjabvttkt9+vRJrVq1Srfeemud7fFYQ7ezzjqrUmb55ZefZfsZZ5xRZz8vvvhi2mSTTVLHjh1T375905lnnjnLudx0001pwIABucyaa66Zhg0bVuCVAwAA0Nw0utD9xRdfpLXXXjtdfPHFDW7/8MMP69yuvPLKHKp33nnnOuVOPfXUOuV+8YtfVLZNmDAhbbXVVqlfv37p2WefzYH95JNPTpdddlmlzOOPP5523XXXtO+++6aRI0emHXbYId9efvnlAq8eAACA5qRtamS22WabfJud3r1717l/2223pc033zytsMIKdR7v3LnzLGXLrr322jRt2rQc2Nu3b59WX3319Pzzz6dzzz03HXDAAbnMBRdckLbeeut01FFH5funnXZauu+++9JFF12Uhg4duhCuFAAAgOau0YXu+fHRRx+lu+66K/3pT3+aZVt0J4+gvNxyy6XddtstHX744alt2/93uSNGjEibbrppDtxlQ4YMSb/73e/SZ599lrp165bLHHHEEXX2GWXqd3evNnXq1HyrblEPM2fOzLfGplQq5V4CqVRKqVTw+f3nWHHMxvhasOhFPVAfqAV1j1pR96gVdY9amtmM69+8XlOTDt0RtqNFe6eddqrz+C9/+cu03nrrpSWXXDJ3Ez/22GNzF/NoyQ5jxoxJ/fv3r/OcXr16VbZF6I6f5ceqy8Tjs3P66aenU045ZZbHx40bl6ZMmZIam0mTJqWePbqnTjMmp1YTPy30WHGM/sv1TRMnTkxjx44t9Fg0nQ+p8ePH5w/h1q0b3UgXmjF1j1pR96gVdY9amtmM619km2YfuqN7+O67754nOqtW3UK91lpr5Rbtn/3sZzkUd+jQobDziXBffexo6Y5J2nr06JG6dOmSGpscgMd9nPq06ZS6dl6y0GNN/nRCGjX6vfwlSc+ePQs9Fk3nAzh6P8TfR3P7AKZxU/eoFXWPWlH3qKWZzbj+1c+hzS50P/roo+n1119PN9xww1zLbrjhhmn69OnpnXfeSausskoe6x1d06uV75fHgc+uzOzGiYcI9A2F+qhcjbGClbt7p+hi3qrg8/vPseKYjfG1oDbK9UGdYFFT96gVdY9aUfeopVbNtP7N6/U02au+4oor0vrrr59nOp+bmCQtXpByC+vAgQPz0mRfffVVpUxMkhaBPLqWl8sMHz68zn6iTDwOAAAATTJ0xzjjCMlxC6NGjcq/jx49uk637VhDe7/99pvl+TEB2vnnn59eeOGF9Pbbb+eZymMStT322KMSqGNitehyHsuBvfLKK7m1PGYrr+4afuihh6a77747nXPOOem1117LS4o988wz6ZBDDlkkrwMAAABNX6PrXh7BNpYAKysH4b322itdffXV+ffrr78+d1WOdbTri+7dsT1CcswkHhOmReiuDtRdu3ZN9957bzr44INza3n37t3TiSeeWFkuLGy00UbpuuuuS8cff3w67rjj0je/+c08c/kaa6xR8CsAAABAc9HoQvegQYP+3zjjOYhwXB2Qq8Ws5U888cRcjxMTrMW48DnZZZdd8g0AAACaRfdyAAAAaC6EbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgII0udD/yyCNpu+22S3369EmtWrVKt956a53tP/3pT/Pj1bett966TplPP/007b777qlLly5piSWWSPvuu2+aNGlSnTIvvvhi2mSTTVLHjh1T375905lnnjnLudx0001pwIABucyaa66Zhg0bVtBVAwAA0Bw1utD9xRdfpLXXXjtdfPHFsy0TIfvDDz+s3P7617/W2R6B+5VXXkn33XdfuvPOO3OQP+CAAyrbJ0yYkLbaaqvUr1+/9Oyzz6azzjornXzyyemyyy6rlHn88cfTrrvumgP7yJEj0w477JBvL7/8ckFXDgAAQHPTNjUy22yzTb7NSYcOHVLv3r0b3Pavf/0r3X333enpp59OG2ywQX7swgsvTN///vfT2WefnVvQr7322jRt2rR05ZVXpvbt26fVV189Pf/88+ncc8+thPMLLrggh/ujjjoq3z/ttNNyiL/ooovS0KFDF/p1AwAA0Pw0utA9Lx566KHUs2fP1K1bt7TFFluk3/zmN2mppZbK20aMGJG7lJcDdxg8eHBq3bp1evLJJ9OOO+6Yy2y66aY5cJcNGTIk/e53v0ufffZZ3m+UOeKII+ocN8rU7+5eberUqflW3aIeZs6cmW+NTalUyt3zU6mUUqng8/vPseKYjfG1YNGLeqA+UAvqHrWi7lEr6h61NLMZ1795vaYmF7qj9XmnnXZK/fv3T2+99VY67rjjcst4hOQ2bdqkMWPG5EBerW3btmnJJZfM20L8jOdX69WrV2VbhO74WX6sukx5Hw05/fTT0ymnnDLL4+PGjUtTpkxJjU2Mc+/Zo3vqNGNyajXx00KPFcfov1zfNHHixDR27NhCj0XT+ZAaP358/hCOL8VgUVH3qBV1j1pR96ilmc24/kW2aZah+8c//nHl95jcbK211korrrhibv3ecssta3puxx57bJ3W8WjpjknaevTokSd1a2xyAB73cerTplPq2nnJQo81+dMJadTo91Lnzp1n+VKElvsBHL0f4u+juX0A07ipe9SKuketqHvU0sxmXP9iwu1mGbrrW2GFFVL37t3Tm2++mUN3jPWu35I6ffr0PKN5eRx4/Pzoo4/qlCnfn1uZ2Y0lL481j1t9UbkaYwUrd/dO0cW8VcHn959jxTEb42tBbZTrgzrBoqbuUSvqHrWi7lFLrZpp/ZvX62nyV/3vf/87ffLJJ2nppZfO9wcOHJg+//zzPCt52QMPPJC/Ydlwww0rZWJG86+++qpSJiZJW2WVVXLX8nKZ4cOH1zlWlInHAQAAoEmG7hhnHDOJxy2MGjUq/z569Oi8LWYTf+KJJ9I777yTQ/H222+fVlpppTzJWVh11VXzuO/9998/PfXUU+mxxx5LhxxySO6WHjOXh9122y1PohbLgcXSYjfccEOerby6a/ihhx6aZ0E/55xz0muvvZaXFHvmmWfyvgAAAKBJhu4Ituuuu26+hQjC8fuJJ56YJ0p78cUX0w9+8IO08sor59C8/vrrp0cffbROt+5YEmzAgAG5u3ksFbbxxhvXWYO7a9eu6d57782BPp5/5JFH5v1Xr+W90UYbpeuuuy4/L9YN/9vf/pZnLl9jjTUW8SsCAABAU9XoxnQPGjTo/40zno177rlnrvuImcojMM9JTMAWYX1Odtlll3wDAACAZtHSDQAAAM2F0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAtJTQ/cgjj6Ttttsu9enTJ7Vq1SrdeuutlW1fffVVOuaYY9Kaa66ZvvGNb+Qye+65Z/rggw/q7GP55ZfPz62+nXHGGXXKvPjii2mTTTZJHTt2TH379k1nnnnmLOdy0003pQEDBuQyccxhw4YVeOUAAAA0N40udH/xxRdp7bXXThdffPEs27788sv03HPPpRNOOCH/vPnmm9Prr7+efvCDH8xS9tRTT00ffvhh5faLX/yism3ChAlpq622Sv369UvPPvtsOuuss9LJJ5+cLrvsskqZxx9/PO26665p3333TSNHjkw77LBDvr388ssFXj0AAADNSdvUyGyzzTb51pCuXbum++67r85jF110Ufr2t7+dRo8enZZbbrnK4507d069e/ducD/XXnttmjZtWrryyitT+/bt0+qrr56ef/75dO6556YDDjggl7ngggvS1ltvnY466qh8/7TTTsvHjuMNHTp0IV4xAAAAzVWja+meX+PHj8/dx5dYYok6j0d38qWWWiqtu+66uSV7+vTplW0jRoxIm266aQ7cZUOGDMmt5p999lmlzODBg+vsM8rE4wAAANAkW7rnx5QpU/IY7+gG3qVLl8rjv/zlL9N6662XllxyydxN/Nhjj81dzKMlO4wZMyb179+/zr569epV2datW7f8s/xYdZl4fHamTp2ab9Xd2MPMmTPzrbEplUr5C4tUKqVUKvj8/nOsOGZjfC1Y9KIeqA/UgrpHrah71Iq6Ry3NbMb1b16vqcmG7phU7Yc//GF+Ay+99NI624444ojK72uttVZu0f7Zz36WTj/99NShQ4fCzin2f8opp8zy+Lhx4/IXBI3NpEmTUs8e3VOnGZNTq4mfFnqsOEb/5fqmiRMnprFjxxZ6LJrOh1T0VIm/4datm3ynG5oQdY9aUfeoFXWPWprZjOtfZJtmG7rLgfvdd99NDzzwQJ1W7oZsuOGGuXv5O++8k1ZZZZU81vujjz6qU6Z8vzwOfHZlZjdOPESLenXgj5bumBm9R48ecz3HWsgBeNzHqU+bTqlr5yULPdbkTyekUaPfy2Pte/bsWeixaDofwNH7If4+mtsHMI2buketqHvUirpHLc1sxvUvVrlqlqG7HLjfeOON9OCDD+Zx23MTk6TFG1wOewMHDky//vWv877atWuXH4tJ0iKQR9fycpnhw4enww47rLKfKBOPz060ojfUkh7HbowVrNzdO0UX81YFn99/jhXHbIyvBbVRrg/qBIuauketqHvUirpHLbVqpvVvXq+nbWPs8vzmm29W7o8aNSqH5hifvfTSS6f//u//zsuF3XnnnWnGjBmVMdaxPbqRx0RnTz75ZNp8881zq2rcP/zww9Mee+xRCdS77bZb7gYey4HFmPBYBixmKz/vvPMqxz300EPTZpttls4555y07bbbpuuvvz4988wzdZYVAwAAgCYVuiPYRmAuK3fX3muvvfJa2rfffnu+v84669R5XrR6Dxo0KLc0R0COsjGpWUyYFqG7utt3LD127733poMPPjitv/76qXv37unEE0+sLBcWNtpoo3Tdddel448/Ph133HHpm9/8Zrr11lvTGmussQheBQAAAJqDRhe6IzjnLs+zMadtIWYtf+KJJ+Z6nJhg7dFHH51jmV122SXfAAAA4OtoXp3qAQAAoBERugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIG0X1o4mTpyYxo8fn2bOnDnLtuWWW25hHQYAAABaTui+9NJL07nnnpvefvvt2ZaZMWPGgh4GAAAAWlb38qFDh6aDDz44rbTSSuk3v/lNKpVK6bDDDkv/8z//k3r37p3WXnvtdMUVVyy8swUAAICWErovvPDCNGTIkPSPf/wjHXDAAfmxbbfdNv3v//5vevXVV3OX808++WRhnSsAAAC0nND91ltvpe222y7/3q5du/xz2rRp+WfXrl3Tfvvtly655JKFcZ4AAADQskJ3BOvp06fn37t06ZIWW2yx9N5771W2d+7cOY0ZM2bBzxIAAABaWuheY4010gsvvFC5/53vfCdPrPb+++/n8P2HP/whrbzyygvjPAEAAKBlzV6+xx575MnUpk6dmjp06JBOOeWUNHjw4MoSYdHl/O9///vCOlcAAABoOaF77733zrey7373u+mVV15Jd9xxR2rTpk3aaquttHQDAADQYi3wOt31rbDCCunQQw9d2LsFAACAljWmGwAAAFhILd2tW7fOty+//DK1b98+/96qVas5Pie2l2c4BwAAgJZkvkL3iSeemEN027Zt69wHAAAAFjB0n3zyyXO8DwAAAPz/jOkGAACAxhi6f//736chQ4bMdvs222yTLr300gU5BAAAALTM0H3FFVek1VZbbbbbY9tll122IIcAAACAlhm633rrrbTqqqvOdvuAAQNyGQAAAGiJFih0x7JhY8aMme32Dz/8MC8rBgAAAC3RAiXi73znO+nqq69OEydOnGXb+PHj01VXXZXLAAAAQEs0X0uG1XfSSSelzTbbLK2zzjrpsMMOS6uvvnp+/OWXX07nn39+bum+7rrrFta5AgAAQMsJ3RtuuGG644470s9+9rN06KGHplatWuXHS6VS6t+/f7r99tvTwIEDF9a5AgAAQMsJ3eF73/teevPNN9PIkSMrk6atuOKKab311quEcAAAAGiJFjh0h5gsbf311883AAAAYCGG7ldffTW9/fbb6bPPPstdy+vbc889F8ZhAAAAoOWE7uhOvscee6SnnnqqwbAdoou50A0AAEBLtEChOyZQe+mll/JM5Ztssknq1q3bwjszAAAAaMmh+7HHHkvHHXdc+sUvfrHwzggAAACaidYL8uTu3bunrl27LryzAQAAgGZkgUL3gQcemK655po0Y8aMhXdGAAAA0EwsUPfylVdeOQfutddeO+2zzz6pb9++qU2bNrOU22mnnRbkMAAAANDyQvePfvSjyu+/+tWvZjt7uZZwAAAAWqIFCt0PPvjgwjsTAAAAaGYWKHRvttlmC+9MAAAAoJlZoInUyqZOnZpGjBiRbrvttvTxxx8v0L4eeeSRtN1226U+ffrkrum33nprne2lUimdeOKJaemll06dOnVKgwcPTm+88UadMp9++mnafffdU5cuXdISSyyR9t133zRp0qQ6ZV588cW8tnjHjh3zWPQzzzxzlnO56aab0oABA3KZNddcMw0bNmyBrg0AAICWZYFD9+9///scgDfeeOM8YVqE2RDhO5YUu/LKK+drf1988UWemO3iiy9ucHuE4zjm0KFD05NPPpm+8Y1vpCFDhqQpU6ZUykTgfuWVV9J9992X7rzzzhzkDzjggMr2CRMmpK222ir169cvPfvss+mss85KJ598crrssssqZR5//PG066675sA+cuTItMMOO+Tbyy+//DVeJQAAAFqi+Q7d0aocQTZcddVV6bDDDktbb711uuKKK3IrdFkE7i222CJdf/3187X/bbbZJv3mN79JO+644yzbYv/nn39+Ov7449P222+f1lprrfTnP/85ffDBB5UW8X/961/p7rvvTpdffnnacMMN85cBF154YT6PKBeuvfbaNG3atPyFwOqrr55+/OMfp1/+8pfp3HPPrRzrggsuyNd11FFHpVVXXTWddtppab311ksXXXTR/L5kAAAAtFDzHLoj8J599tlp0003TYsttlh+7Jxzzsnh97rrrstdwutbf/31c4vzwjJq1Kg0ZsyY3KW8rGvXrjlcR/f2ED+jS/kGG2xQKRPlW7dunVvGy2XiOtq3b18pE63lr7/+evrss88qZaqPUy5TPg4AAAAstInUogt2tB4/9thjaYUVVsiPvfnmm7mFeHaWXHLJ9Mknn6SFJQJ36NWrV53H4355W/zs2bNnne1t27bN51Jdpn///rPso7ytW7du+eecjjO7se1xq+7GHmbOnJlvjU18kRLj5lP0UCgVfH7/OVYcszG+Fix6UQ/UB2pB3aNW1D1qRd2jlmY24/o3r9c0z6E7xlnHBGV//etf069//ev8WLQoz2nitFdffTX17t07tRSnn356OuWUU2Z5fNy4cXXGnDcWMblczx7dU6cZk1OriZ8Weqw4Rv/l+qaJEyemsWPHFnosms6H1Pjx4/OHcPREgUVF3aNW1D1qRd2jlmY24/oX2Wahhu7oWv3888+nn/3sZ7lb+ZFHHpm+//3v58nHfv7zn89SPrqV//GPf0z77LNPWljKAf6jjz7Kk7eVxf111lmnUqZ+qJs+fXr+wqD8/PgZz6lWvj+3MnP6EuHYY49NRxxxRJ2W7pgZvUePHnkm9cYmB+BxH6c+bTqlrp2XLPRYkz+dkEaNfi917tx5lp4ItNwP4Oj9EH8fze0DmMZN3aNW1D1qRd2jlmY24/oXq1wt9HW6l1122XTXXXdVJiSLCc9iPPUaa6yRx3THi/mnP/0pT1D297//PQfjWN5rYYku4RF6hw8fXgnZEWxjrPZBBx2U7w8cODB9/vnneVbyGFMeHnjggfxmx7mWy0Rr/VdffZXatWuXH4uZzldZZZXctbxcJo4TE8WVRZl4fHY6dOiQb/VF5WqMFazc3TtFF/NWBZ/ff44Vx2yMrwW1Ua4P6gSLmrpHrah71Iq6Ry21aqb1b16v52tddayhXf4Z4TZm+b7hhhtyqPrLX/6S7rjjjrzc1hNPPJFnMZ/fLs/Roh638uRp8fvo0aPzmxUhOML+7bffnl566aW055575vOI5bxCzDQe57P//vunp556Ko9BP+SQQ/IM5eXz3m233fIkarEcWLTIx7nHbOXVrdSHHnpongU9WvVfe+21vKTYM888k/cFAAAAC72luyHRVTgmWItbjF2OFuUF6ToQwXbzzTev3C8H4b322itdffXV6eijj85rece629GiHUuCRTiubtqPJcEiHG+55Zb5PHbeeee8tnf1jOf33ntvOvjgg3NreHwxEC3y1Wt5b7TRRnlW9lie7Ljjjkvf/OY387Jk0aoPAAAAiyR0V4uwvaAGDRpUZ73v+qK1+9RTT8232YmZyiMwz0ms8f3oo4/Oscwuu+ySbwAAALDIQ/ecgm91SD7hhBMW5DAAAADQ8kJ3jHOe2yRdQjcAAAAt1QJNHxfjt+vfYnmut956Kx1++OFpgw02sCYzAAAALdZCn7M9Ji6Lpb3OPvvsPPnYL37xi4V9CAAAAGgSCl0obdNNN03Dhg0r8hAAAADQMkN3LP/V3BZABwAAgEUykdqf//znBh+P9bMfeeSRdPPNN6f99ttvQQ4BAAAALTN0//SnP53ttu7du6f/+Z//SSeeeOKCHAIAAABaZugeNWrULI/FEmHdunVLnTt3XpBdAwAAQMsO3f369Vt4ZwIAAADNjFnOAAAAoDG2dMfM5NGdfH5E+enTpy/IYQEAAKD5h+6YJO3WW29Nr7zyShoyZEhaZZVV8uOvvfZauvfee9Maa6yRdthhh4V1rgAAANByQnefPn3S2LFj08svv1wJ3GX/+te/0hZbbJHL7L///gt6ngAAANCyxnSfddZZ6ZBDDpklcIdVV101bzvzzDMX5BAAAADQMkP3v//979SuXbvZbo9tUQYAAABaogUK3TFm+5JLLknvv//+LNsibMe2Nddcc0EOAQAAAC1zTPd5552XJ1BbeeWV04477phWWmml/Pgbb7yRJ1grlUrpmmuuWVjnCgAAAC0ndG+88cbpySefTCeccEK65ZZb0uTJk/PjnTp1ymH8lFNO0dINAABAi7VAobvcxTwC98yZM9O4cePyYz169MhreAMAAEBLtsChuyxCdseOHdPiiy8ucAMAAMCCTqQWnnnmmbT11lunxRZbLC211FLp4Ycfzo9//PHHafvtt08PPfTQwjhPAAAAaFmh+/HHH8/jumPitD322CN3MS/r3r17Gj9+fPrDH/6wMM4TAAAAWlboPu6449Kqq66aXn311fTb3/52lu2bb755nmgNAAAAWqIFCt1PP/102nvvvVOHDh1Sq1atZtm+zDLLpDFjxizIIQAAAKBlhu527drV6VJe3/vvv58nVgMAAICWaIFC93e+8530t7/9rcFtX3zxRbrqqqvSZptttiCHAAAAgJYZuk855ZQ8e/m2226b/vGPf+THXnjhhXT55Zen9ddfP6/bfcIJJyyscwUAAICWs073hhtumIYNG5YOOuigtOeee+bHjjzyyPxzxRVXzNvWWmuthXOmAAAA0FJCd6lUShMnTkwbbbRRev3119Pzzz+flw6LMd4RuKOlu6HJ1QAAAKCl+Nqhe9q0aWnJJZfMS4UdffTRaZ111sk3AAAAYAHHdMcyYb17984/AQAAgIU8kdpPf/rT9Oc//zm3egMAAAALcSK1NddcM916661p9dVXzwF8+eWXT506dZql3E477bQghwEAAICWF7p33XXXyu+zWxosJlObMWPGghwGAAAAWkboPu6449KPf/zjvBTYgw8+WMxZAQAAQEsM3WeccUZaY401cujebLPN0ieffJJ69uyZ7rvvvrTFFlsUc5YAAADQ0iZSq16zGwAAACggdAMAAACzEroBAACgMc1e/s4776Tnnnsu/z5+/Pj884033khLLLFEg+XXW2+9BTlHAAAAaDmhO5YHq79E2M9//vMGx3pbMgwAAICWar5D91VXXVXMmQAAAEBLD9177bVXMWcCAAAAzYyJ1AAAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBNLnQvv/zyqVWrVrPcDj744Lx90KBBs2w78MAD6+xj9OjRadttt02LLbZY6tmzZzrqqKPS9OnT65R56KGH0nrrrZc6dOiQVlpppXT11Vcv0usEAACg6Wubmpinn346zZgxo3L/5ZdfTt/73vfSLrvsUnls//33T6eeemrlfoTrsnhuBO7evXunxx9/PH344Ydpzz33TO3atUu//e1vc5lRo0blMhHWr7322jR8+PC03377paWXXjoNGTJkkV0rAAAATVuTC909evSoc/+MM85IK664Ytpss83qhOwI1Q25995706uvvpruv//+1KtXr7TOOuuk0047LR1zzDHp5JNPTu3bt09Dhw5N/fv3T+ecc05+zqqrrpr++c9/pvPOO0/oBgAAoPmG7mrTpk1L11xzTTriiCNyN/KyaJ2OxyN4b7fddumEE06otHaPGDEirbnmmjlwl0WQPuigg9Irr7yS1l133Vxm8ODBdY4VZQ477LA5ns/UqVPzrWzChAn558yZM/OtsSmVSv/vdSuVUioVfH7/OVYcszG+Fix6UQ/UB2pB3aNW1D1qRd2jlmY24/o3r9fUpEP3rbfemj7//PP005/+tPLYbrvtlvr165f69OmTXnzxxdyC/frrr6ebb745bx8zZkydwB3K92PbnMpEiJ48eXLq1KlTg+dz+umnp1NOOWWWx8eNG5emTJmSGptJkyalnj26p04zJqdWEz8t9FhxjP7L9U0TJ05MY8eOLfRYNJ0PqfHjx+cP4datm9z0EjRh6h61ou5RK+oetTSzGde/yDbNPnRfccUVaZtttskBu+yAAw6o/B4t2jEOe8stt0xvvfVW7oZepGOPPTa3updFSO/bt2/uEt+lS5fU2OQAPO7j1KdNp9S185KFHmvypxPSqNHvpc6dO+fJ6yA+gKP3Q/x9NLcPYBo3dY9aUfeoFXWPWprZjOtfx44dm3fofvfdd/O47HIL9uxsuOGG+eebb76ZQ3d0OX/qqafqlPnoo4/yz/I48PhZfqy6TATn2bVyh5jpPG71ReVqjBWs3N07RRfzVgWf33+OFcdsjK8FtVGuD+oEi5q6R62oe9SKukcttWqm9W9er6fJXvVVV12VW0xjlvE5ef755/PPaPEOAwcOTC+99FKdLs733XdfDtSrrbZapUzMWF4tysTjAAAAMK9aN9UuChG699prr9S27f/fWB9dyGMm8meffTa988476fbbb8/LgW266aZprbXWymW22mqrHK5/8pOfpBdeeCHdc8896fjjj8/rfJdbqWOpsLfffjsdffTR6bXXXkuXXHJJuvHGG9Phhx9es2sGAACg6WmSoTu6lY8ePTrts88+dR6P5b5iWwTrAQMGpCOPPDLtvPPO6Y477qiUadOmTbrzzjvzz2i53mOPPXIwr17XO5YLu+uuu3Lr9tprr52XDrv88sstFwYAAMB8aZJjuiNU57HI9cSkZQ8//PBcnx+zmw8bNmyOZQYNGpRGjhy5QOcJAABAy9YkW7oBAACgKRC6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCNLnQffLJJ6dWrVrVuQ0YMKCyfcqUKenggw9OSy21VFp88cXTzjvvnD766KM6+xg9enTadttt02KLLZZ69uyZjjrqqDR9+vQ6ZR566KG03nrrpQ4dOqSVVlopXX311YvsGgEAAGgemlzoDquvvnr68MMPK7d//vOflW2HH354uuOOO9JNN92UHn744fTBBx+knXbaqbJ9xowZOXBPmzYtPf744+lPf/pTDtQnnnhipcyoUaNymc033zw9//zz6bDDDkv77bdfuueeexb5tQIAANB0tU1NUNu2bVPv3r1neXz8+PHpiiuuSNddd13aYost8mNXXXVVWnXVVdMTTzyRvvOd76R77703vfrqq+n+++9PvXr1Suuss0467bTT0jHHHJNb0du3b5+GDh2a+vfvn84555y8j3h+BPvzzjsvDRkyZJFfLwAAAE1Tkwzdb7zxRurTp0/q2LFjGjhwYDr99NPTcsstl5599tn01VdfpcGDB1fKRtfz2DZixIgcuuPnmmuumQN3WQTpgw46KL3yyitp3XXXzWWq91EuEy3eczJ16tR8K5swYUL+OXPmzHxrbEqlUu6en0qllEoFn99/jhXHbIyvBYte1AP1gVpQ96gVdY9aUfeopZnNuP7N6zU1udC94YYb5u7gq6yySu5afsopp6RNNtkkvfzyy2nMmDG5pXqJJZao85wI2LEtxM/qwF3eXt42pzIRoidPnpw6derU4LlF+I/zqW/cuHF5rHljM2nSpNSzR/fUacbk1Grip4UeK47Rf7m+aeLEiWns2LGFHoum8yEVvVPiQ7h16yY50oUmSt2jVtQ9akXdo5ZmNuP6F9mmWYbubbbZpvL7WmutlUN4v3790o033jjbMLyoHHvssemII46o3I+Q3rdv39SjR4/UpUuX1NjkADzu49SnTafUtfOShR5r8qcT0qjR76XOnTvnyesgPoCj90P8fTS3D2AaN3WPWlH3qBV1j1qa2YzrX/S8bpahu75o1V555ZXTm2++mb73ve/lCdI+//zzOq3dMXt5eQx4/Hzqqafq7KM8u3l1mfoznsf9CM5zCvYx03nc6ovK1RgrWLm7d4ou5q0KPr//HCuO2RhfC2qjXB/UCRY1dY9aUfeoFXWPWmrVTOvfvF5Pk7/q6CL91ltvpaWXXjqtv/76qV27dmn48OGV7a+//npeIizGfof4+dJLL9Xp4nzfffflQL3aaqtVylTvo1ymvA8AAABolqH7V7/6VV4K7J133slLfu24446pTZs2adddd01du3ZN++67b+7i/eCDD+aJ1fbee+8clmMStbDVVlvlcP2Tn/wkvfDCC3kZsOOPPz6v7V1upT7wwAPT22+/nY4++uj02muvpUsuuSR3X4/lyAAAAGBeNbnu5f/+979zwP7kk0/yuICNN944LwcWv4dY1iua+Xfeeec8k3jMOh6huSwC+p133plnK48w/o1vfCPttdde6dRTT62UieXC7rrrrhyyL7jggrTsssumyy+/3HJhAAAANO/Qff311891MPvFF1+cb7MTE68NGzZsjvsZNGhQGjly5Nc+TwAAAGhy3csBAACgqRC6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCNLnQffrpp6dvfetbqXPnzqlnz55phx12SK+//nqdMoMGDUqtWrWqczvwwAPrlBk9enTadttt02KLLZb3c9RRR6Xp06fXKfPQQw+l9dZbL3Xo0CGttNJK6eqrr14k1wgAAEDz0ORC98MPP5wOPvjg9MQTT6T77rsvffXVV2mrrbZKX3zxRZ1y+++/f/rwww8rtzPPPLOybcaMGTlwT5s2LT3++OPpT3/6Uw7UJ554YqXMqFGjcpnNN988Pf/88+mwww5L++23X7rnnnsW6fUCAADQdLVNTczdd99d536E5WipfvbZZ9Omm25aeTxasHv37t3gPu6999706quvpvvvvz/16tUrrbPOOum0005LxxxzTDr55JNT+/bt09ChQ1P//v3TOeeck5+z6qqrpn/+85/pvPPOS0OGDCn4KgEAAGgOmlxLd33jx4/PP5dccsk6j1977bWpe/fuaY011kjHHnts+vLLLyvbRowYkdZcc80cuMsiSE+YMCG98sorlTKDBw+us88oE48DAABAs2zprjZz5szc7fu73/1uDtdlu+22W+rXr1/q06dPevHFF3MLdoz7vvnmm/P2MWPG1AncoXw/ts2pTATzyZMnp06dOs1yPlOnTs23sihbPs+4NTalUimPd0+lUkqlgs/vP8eKYzbG14JFL+qB+kAtqHvUirpHrah71NLMZlz/5vWamnTojrHdL7/8cu72Xe2AAw6o/B4t2ksvvXTacsst01tvvZVWXHHFQid5O+WUU2Z5fNy4cWnKlCmpsZk0aVLq2aN76jRjcmo18dNCjxXH6L9c3zRx4sQ0duzYQo9F0/mQip4q8SHcunWT73RDE6LuUSvqHrWi7lFLM5tx/Yts06xD9yGHHJLuvPPO9Mgjj6Rll112jmU33HDD/PPNN9/MoTvGej/11FN1ynz00Uf5Z3kcePwsP1ZdpkuXLg22cofoxn7EEUfUaenu27dv6tGjR35eY5MD8LiPU582nVLXznW75y9skz+dkEaNfq8y6zzEB3D0foi/j+b2AUzjpu5RK+oetaLuUUszm3H969ixY/MM3fENyS9+8Yt0yy235CW9YrKzuYnZx0O0eIeBAwem//3f/80truUAGDOhRzBebbXVKmWGDRtWZz9RJh6fnVhaLG71ReVqjBWs3N07RRfzVgWf33+OFcdsjK8FtVGuD+oEi5q6R62oe9SKukcttWqm9W9er6d1U+xSfs0116Trrrsut5rG2Ou4xTjrEF3IYybymM38nXfeSbfffnvac88988zma621Vi4TS4xFuP7JT36SXnjhhbwM2PHHH5/3XQ7Nsa7322+/nY4++uj02muvpUsuuSTdeOON6fDDD6/p9QMAANB0NLnQfemll+YxAYMGDcot1+XbDTfckLfHcl+xFFgE6wEDBqQjjzwy7bzzzumOO+6o7KNNmza5a3r8jJbrPfbYIwfzU089tVImWtDvuuuu3Lq99tpr56XDLr/8csuFAQAAMM+aZPfyOYkx1A8//PBc9xOzm9fvPl5fBPuRI0fO9zkCAABAk2zpBgAAgKZC6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAAABRE6AYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFKRtUTuG+qZNm5refffdRXKsLl26pB49eiySYwEAAMyO0M0iMfHzT9Oot95Ovz7tt6lDhw6FH69zp47pysuGCt4AAEBNCd0sElO+/CK1btcubfaTg9Iyy69Y6LE++fDf6aG/XJomTJggdAMAADUldLNILdW7T+rdr3+tTwMAAGCRMJEaAAAAFEToBgAAgIII3QAAAFAQoRsAAAAKInQDAABAQYRuAAAAKIjQDQAAAAURugEAAKAgQjcAAAAUROgGAACAggjdAAAAUJC2Re0YamnatKnp3XffXSTH6tKlS+rRo8ciORYAANC0CN00OxM//zSNeuvt9OvTfps6dOhQ+PE6d+qYrrxsqOANAADMQuim2Zny5Repdbt2abOfHJSWWX7FQo/1yYf/Tg/95dI0YcIEoRsAAJiF0E2ztVTvPql3v/61Pg0AAKAFM5HaXFx88cVp+eWXTx07dkwbbrhheuqpp2p9SgAAADQRQvcc3HDDDemII45IJ510UnruuefS2muvnYYMGZLGjh1b61MDAACgCRC65+Dcc89N+++/f9p7773TaqutloYOHZoWW2yxdOWVV9b61AAAAGgCjOmejWnTpqVnn302HXvssZXHWrdunQYPHpxGjBhR03Oj5S5PFvWyffv2zeJYpVIpTZw4MU2aNCl99dVXzea6ank8y9cBADQ+QvdsfPzxx2nGjBmpV69edR6P+6+99lqDz5k6dWq+lY0fPz7//Pzzz9PMmTNTYxOBJ8LOB2+/niZ/ManQY40dPSpSVvpg1JupNH16sznWv998LS9P9j8nn5Y6dGhfeHj74L3Radl+/VPbtm2a/LFapVapX99l0xtvv53eH/1us7muWh6vQ+vW6bhjjkrdunUr/FhNXXz+ffjhh7U+DVogdY9aUfdoivWvW7duaYkllkiNVaxgVG5MmpNWpbmVaKE++OCDtMwyy6THH388DRw4sPL40UcfnR5++OH05JNPzvKck08+OZ1yyimL+EwBAAColffeey8tu+yys92upXs2unfvntq0aZM++uijOo/H/d69ezf4nOiKHhOvlUXr9qeffpqWWmqp1KpVq9QYv5np27dvriTRLRUWJfWPWlH3qBV1j1pR96ilCc24/pWHS/bp02eO5YTu2YgxmOuvv34aPnx42mGHHSohOu4fcsghDT6nQ4cO+VatMXeHKIvK39z+AGg61D9qRd2jVtQ9akXdo5a6NNP617Vr17mWEbrnIFqt99prr7TBBhukb3/72+n8889PX3zxRZ7NHAAAAOZG6J6DH/3oR2ncuHHpxBNPTGPGjEnrrLNOuvvuu2eZXA0AAAAaInTPRXQln1138qYuusKfdNJJs3SJh0VB/aNW1D1qRd2jVtQ9aqmD+mf2cgAAAChK68L2DAAAAC2c0A0AAAAFEboBAACgIEJ3C3bxxRen5ZdfPnXs2DFtuOGG6amnnqr1KdGIPPLII2m77bZLffr0Sa1atUq33nprne0xHUTM7L/00kunTp06pcGDB6c33nijTplPP/007b777nlNxlizft99902TJk2qU+bFF19Mm2yySa6Hffv2TWeeeeYs53LTTTelAQMG5DJrrrlmGjZs2HyfC03H6aefnr71rW+lzp07p549e6Yddtghvf7663XKTJkyJR188MFpqaWWSosvvnjaeeed00cffVSnzOjRo9O2226bFltssbyfo446Kk2fPr1OmYceeiitt956eXKXlVZaKV199dXz/Vk5L+dC03DppZemtdZaq7KW7MCBA9M//vGPynb1jkXljDPOyP/vPeywwyqPqX8U5eSTT871rfoW/+4qU/cWgphIjZbn+uuvL7Vv37505ZVXll555ZXS/vvvX1piiSVKH330Ua1PjUZi2LBhpV//+telm2++OSZbLN1yyy11tp9xxhmlrl27lm699dbSCy+8UPrBD35Q6t+/f2ny5MmVMltvvXVp7bXXLj3xxBOlRx99tLTSSiuVdt1118r28ePHl3r16lXafffdSy+//HLpr3/9a6lTp06lP/zhD5Uyjz32WKlNmzalM888s/Tqq6+Wjj/++FK7du1KL7300nydC03HkCFDSldddVWuE88//3zp+9//fmm55ZYrTZo0qVLmwAMPLPXt27c0fPjw0jPPPFP6zne+U9poo40q26dPn15aY401SoMHDy6NHDky1+fu3buXjj322EqZt99+u7TYYouVjjjiiFy3LrzwwlzX7r777vn6rJzbudB03H777aW77rqr9H//93+l119/vXTcccflz5uoi0G9Y1F46qmnSssvv3xprbXWKh166KGVx9U/inLSSSeVVl999dKHH35YuY0bN66yXd1bcEJ3C/Xtb3+7dPDBB1fuz5gxo9SnT5/S6aefXtPzonGqH7pnzpxZ6t27d+mss86qPPb555+XOnTokINziA/UeN7TTz9dKfOPf/yj1KpVq9L777+f719yySWlbt26laZOnVopc8wxx5RWWWWVyv0f/vCHpW233bbO+Wy44Yaln/3sZ/N8LjRtY8eOzXXp4Ycfrry/EYRuuummSpl//etfucyIESPy/fgffuvWrUtjxoyplLn00ktLXbp0qdS3o48+Ov8jo9qPfvSjHPrn9bNyXs6Fpi0+oy6//HL1jkVi4sSJpW9+85ul++67r7TZZptVQrf6R9GhOxpJGqLuLRy6l7dA06ZNS88++2zuglvWunXrfH/EiBE1PTeahlGjRqUxY8bUqUNdu3bN3YDKdSh+RpfyDTbYoFImykdde/LJJytlNt1009S+fftKmSFDhuSuxJ999lmlTPVxymXKx5mXc6FpGz9+fP655JJL5p/x+fXVV1/Vec+jG9xyyy1Xp/7FUIRevXrVqTcTJkxIr7zyyjzVrXn5rJyXc6FpmjFjRrr++uvTF198kbuZq3csCtFtNrro1q8j6h9Fi2F5MaRwhRVWyEMDo7t4UPcWDqG7Bfr444/zPyaq/zBC3I/wAnNTridzqkPxM8b0VGvbtm0OTtVlGtpH9TFmV6Z6+9zOhaZr5syZeUzjd7/73bTGGmvkx+J9jS9q4kudOdWLr1u34h8JkydPnqfPynk5F5qWl156KY8TjDGHBx54YLrlllvSaqutpt5RuPiS57nnnsvzWtSn/lGkaKiI8dV33313ntsiGjRivp2JEyeqewtJ24W1IwAootXn5ZdfTv/85z9rfSq0EKusskp6/vnncw+Lv/3tb2mvvfZKDz/8cK1Pi2buvffeS4ceemi677778gRSsChts802ld9jMskI4f369Us33nhjnqCWBaeluwXq3r17atOmzSwz/cX93r171+y8aDrK9WROdSh+jh07ts72mMUyZjSvLtPQPqqPMbsy1dvndi40TYcccki6884704MPPpiWXXbZyuPxvkY3tM8//3yO9eLr1q2YtTr+kTEvn5Xzci40LdGKErPqrr/++rnFce21104XXHCBekehotts/D8zZnaOXmFxiy97fv/73+ffoyVP/WNRiZbklVdeOb355ps++xYSobuF/oMi/jExfPjwOl04436MW4O56d+/f/5wq65D0T0oxmqX61D8jA/F+IdE2QMPPJDrWnyDWi4TS5PF+Jyy+JY/Wpq6detWKVN9nHKZ8nHm5VxoWmLuvgjc0a036ky8x9Xi86tdu3Z13vOYByDGn1XXv+gmXP3FT9Sb+J97dBWel7o1L5+V83IuNG3xnk+dOlW9o1BbbrllrjvRy6J8izlRYmxt+Xf1j0Ullnd966238lKsPvsWkoU0IRtNTEzJH7M7X3311XmW6QMOOCBPyV896yAtW8ygGss+xC0+Ks4999z8+7vvvltZpivqzG233VZ68cUXS9tvv32DS4atu+66pSeffLL0z3/+M8/IWr1kWMxCGUuG/eQnP8lL8kS9jOUk6i8Z1rZt29LZZ5+dZ6iMGTYbWjJsbudC03HQQQflJeAeeuihOsuXfPnll3WWDIllxB544IG8ZMjAgQPzrf7yJVtttVVediyWJOnRo0eDy5ccddRRuW5dfPHFDS5fMrfPyrmdC03H//zP/+RZ8keNGpU/S+J+rLhw77335u3qHYtS9ezlQf2jKEceeWT+f2589sW/u2Lpr1jyK1YPCereghO6W7BYHy8qbayHF1P0x1rKUPbggw/msF3/ttdee1WW6jrhhBNyaI4PyC233DKva1vtk08+ySF78cUXz8tG7L333jnMV4t1tTfeeOO8j2WWWSYH6PpuvPHG0sorr5zraiw3EevoVpuXc6HpaKjexS3W7i6LL1R+/vOf5+Wc4n/iO+64Yw7m1d55553SNttsk9d+j388xD8qvvrqq1nq+TrrrJPr1gorrFDnGPP6WTkv50LTsM8++5T69euX3+v4B2N8lpQDd1DvqGXoVv8oSizdtfTSS+f3O/4tFvfffPPNynZ1b8G1iv8srFZzAAAA4P9nTDcAAAAUROgGAACAggjdAAAAUBChGwAAAAoidAMAAEBBhG4AAAAoiNANAAAABRG6AQAAoCBCNwAU6J133kmtWrVKV199dWop4npPPvnkr/Xchx56KD8/fgJAcyB0A8B//OAHP0iLLbZYmjhx4mzL7L777ql9+/bpk08+SY3Nq6++msNuBP2v67rrrkvnn3/+Qj0vAGjJhG4AqArUkydPTrfcckuD27/88st02223pa233jottdRSqTGG7lNOOUXoBoBGROgGgKqW7s6dO+fg2ZAI3F988UUO57USx6fxmD59epo2bVqtTwOARkzoBoD/6NSpU9ppp53S8OHD09ixY2fZHmE8QnmE808//TT96le/SmuuuWZafPHFU5cuXdI222yTXnjhhXk61gMPPJA22WST9I1vfCMtscQSafvtt0//+te/6pSJruIxvjlasHfbbbfUrVu3tPHGGze4vxgzvssuu+TfN9988/y8+mOjL7nkkrT66qunDh06pD59+qSDDz44ff7555XtgwYNSnfddVd69913K89ffvnl87YIlieeeGJaf/31U9euXfN5x/k/+OCD6ev697//nXbYYYe8r549e6bDDz88TZ06dZZyjz76aL625ZZbLp973759c9nolTAv4hqjfFxLPH/ZZZdNe+65Z/r444/n69rK4/PPPvvs3BtgxRVXzPuL9yfEa73BBhukjh075m1/+MMfKu9htauuuiptscUW+Zrj+auttlq69NJLv/brCEDj1rbWJwAAjUm0Yv/pT39KN954YzrkkEMqj0fIvueee9Kuu+6aw/krr7ySbr311hwG+/fvnz766KMcsjbbbLMcwiLUzs7999+fA/oKK6yQQ1mExwsvvDB997vfTc8991wl6JbFMb75zW+m3/72t6lUKjW4z0033TT98pe/TL///e/Tcccdl1ZdddX8ePlnHCe6ng8ePDgddNBB6fXXX89B7+mnn06PPfZYateuXfr1r3+dxo8fn8Pweeedl58XXyiECRMmpMsvvzxf//7775/HvV9xxRVpyJAh6amnnkrrrLPOfL3Occ1bbrllGj16dD7veL3+8pe/5C8j6rvpppty1/447+jWH8eL1yvOM7bNyaRJk3KAji809tlnn7TeeuvlsH377bfn53fv3n2+ry1C85QpU9IBBxyQQ/OSSy6ZRo4cmYcdLL300vl1njFjRjr11FNTjx49ZjmneN3jy4/48qZt27bpjjvuSD//+c/TzJkz8xchADQzJQCgYvr06aWll166NHDgwDqPDx06NNJu6Z577sn3p0yZUpoxY0adMqNGjSp16NChdOqpp9Z5LJ531VVXVR5bZ511Sj179ix98sknlcdeeOGFUuvWrUt77rln5bGTTjopP3fXXXedp3O/6aabcvkHH3ywzuNjx44ttW/fvrTVVlvVOeeLLrool7/yyisrj2277balfv36Nfi6TJ06tc5jn332WalXr16lffbZp87jsc849zk5//zzc7kbb7yx8tgXX3xRWmmllWa5hi+//HKW559++umlVq1ald599905HufEE0/M+7v55ptn2TZz5sz5urbye9mlS5f8mlbbbrvtSosttljp/fffrzz2xhtvlNq2bZufU62h6xkyZEhphRVWmOO1ANA06V4OAFXatGmTfvzjH6cRI0bUmZAsupb36tUrt86GaOFs3fr//W80WjVjNvNoFV5llVVya/XsfPjhh+n5559PP/3pT3MLadlaa62Vvve976Vhw4bN8pwDDzxwga4pWtajC/Vhhx1WOecQrbrRLT66lM/L6xKztodokY2W/xjPHN2p53S9sxPXGa3C//3f/115LGaOj9bj+qJnQfWY9mip3mijjXKrf7Qwz8nf//73tPbaa6cdd9xxlm3lbt/ze20777xznRbseP/jNY6u8tU9HFZaaaXco2FO1xM9C+J6oofE22+/ne8D0LwI3QBQT3mitPKEatENOcYVRxiPgFYOZ9EFO7p9RwCPbsoRxF588cU5BqcYLx0inNcXXcEjgNWfLC26ry+I2R0zgmZ0cS9vn5vodh9fDsSY5ejmHdcbgf3rBMU4ZoTS+uOdG3pdogt6+UuK+GIjjhshNczt2G+99VZaY401Fuq11X8/Yvx/dJeP66mvoceiO3908y+P549jxZCAebkeAJoeoRsA6okJtQYMGJD++te/5vvxM1pVq2ctj/HVRxxxRB5Lfc011+Tx3vfdd18eqxuBfGGqbhmtlbjGCL4xQViMd7777rvz9caEYAv7eqtFK3L0AIgAfMwxx+Rx9HHcmDguLIxjz++1Lcj7EV8CRG+J+HLl3HPPzdcVx4qJ3kKRryUAtWEiNQBoQATsE044IbdcR4t3tGh/61vfqmz/29/+lmcJj5BWf6bsaPWenX79+uWfMZFZfa+99lp+brSAfh31W40bOma0bJdFl/NRo0blVte57SOuN55788031ylz0kknfa1zjXN6+eWX85cZ1fur/7q89NJL6f/+7/9yS3TMOF4WQXVeRJCO48zJgl5bzEIeLeRvvvnmLNvqPxaTpsUM7TGRW8zGXrYgs8AD0Lhp6QaABpRbtWMpqRiDXX9t7uhmXn8m8ZhJ+/3335/jfmMcc8yGHSGyermuCIb33ntv+v73v/+1z7kc1qv3GyJUR1fymNm8+pzjC4PozrztttvW2UdDXZzL3eqrn//kk0/mse9fR1znBx98kANvWcxQftlll831uPH7BRdcME/HifHXsYzbLbfcMsu28j4X9Nri+fEaRyt8XFN14P7HP/4x1+uJ1ztmRAegedLSDQANiHG7MVnXbbfdlu/XD93/9V//lZeE2nvvvXO5aJG99tpr67Qkz85ZZ52VJ9gaOHBg2nfffStLhsUa0bG019cVYT5C3e9+97sc5GKseXk96GOPPTYvZRXLWsVSVdGiHOt2R+v9HnvsUadr/Q033JC7zse2GEO93Xbb5euNluCYkCxCerSQDx06NK8xHctyza+YxO2iiy7KrdfPPvts/jIilgyLydSqRTf/aK2ONdHjC42Y+C0mR/vss8/m6ThHHXVUDvax7FosGRbXFxOlRUtznH9MsrYwri3et/jSJJZ9i6XNolt8XF+MJ48vbcq22mqr/AVIvKY/+9nP8v7/+Mc/5vcoJtkDoBmq9fTpANBYXXzxxXm5p29/+9uzbIslw4488si8vFinTp1K3/3ud0sjRowobbbZZvk2pyXDwv3335+fE8+NJahiyalXX321TpnykmHjxo2b53P+4x//mJeeatOmzSxLb8USYQMGDCi1a9cuL4d10EEH5aWxqk2aNKm02267lZZYYon8/PLyYbG81m9/+9t8P5ZFW3fddUt33nlnaa+99pplibF5WTIsxHJfP/jBD/JSW927dy8deuihpbvvvnuW847XZfDgwaXFF188l9t///3zEmsNva4NiaXZDjnkkNIyyyyTl05bdtll83l//PHH83Vt5ffyrLPOavA4w4cPz8+NY6y44oqlyy+/PNeRjh071il3++23l9Zaa638+PLLL1/63e9+l5dti33HMQBoXlrFf2od/AEAmqNYRuyVV15Jb7zxRq1PBYAaMaYbAGAhiGEC1SJox3rkgwYNqtk5AVB7WroBABaCGJceS4+V1z6/9NJL80zlI0eOzLPfA9AymUgNAGAhiEnqYk33MWPG5EnsYqK8WM9d4AZo2bR0AwAAQEGM6QYAAICCCN0AAABQEKEbAAAACiJ0AwAAQEGEbgAAACiI0A0AAAAFEboBAACgIEI3AAAAFEToBgAAgFSM/w9zHy2dlmHAiwAAAABJRU5ErkJggg==", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_after_Valor_total_da_carga.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_before_Quantidade_de_Carga_01.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["  Salvo: hist_after_Quantidade_de_Carga_01.png\n"]}, {"data": {"image/png": "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", "text/plain": ["<Figure size 1000x600 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["for c in cols:\n", "    # Antes\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(df_full[c], bins=30, alpha=0.7, color='skyblue', edgecolor='black')\n", "    plt.title(f\"Antes — {c}\", fontsize=14, fontweight='bold')\n", "    plt.xlabel(c, fontsize=12)\n", "    plt.ylabel(\"Frequência\", fontsize=12)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    \n", "    # Salva o gráfico\n", "    filename = f\"hist_before_{c.replace(' ', '_').replace('-', '_')}.png\"\n", "    plt.savefig(outdir / filename, dpi=300, bbox_inches='tight')\n", "    print(f\"  Salvo: {filename}\")\n", "    plt.show()\n", "    plt.close()\n", "    \n", "    # Depois\n", "    plt.figure(figsize=(10, 6))\n", "    plt.hist(df_scaled[c], bins=30, alpha=0.7, color='lightgreen', edgecolor='black')\n", "    plt.title(f\"Depois — {c} (escalonado)\", fontsize=14, fontweight='bold')\n", "    plt.xlabel(f\"{c} (escalonado)\", fontsize=12)\n", "    plt.ylabel(\"Frequência\", fontsize=12)\n", "    plt.grid(True, alpha=0.3)\n", "    plt.tight_layout()\n", "    \n", "    # Salva o gráfico\n", "    filename = f\"hist_after_{c.replace(' ', '_').replace('-', '_')}.png\"\n", "    plt.savefig(outdir / filename, dpi=300, bbox_inches='tight')\n", "    print(f\"  Salvo: {filename}\")\n", "    plt.show()\n", "    plt.close()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.5"}}, "nbformat": 4, "nbformat_minor": 5}