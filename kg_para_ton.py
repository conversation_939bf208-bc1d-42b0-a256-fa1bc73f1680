import pandas as pd 
import numpy as np
import matplotlib.pyplot as plt

# Carregar os dados
df = pd.read_csv('notebooks\Dados\Graneis-22-23-24(in).csv', sep=';')

def padronizar_unidades_para_toneladas(df, coluna):
    """
    Converte valores de kg para toneladas, tratando formato brasileiro
    """
    df_copy = df.copy()
    
    print(f"Valores originais (amostra): {df_copy[coluna].head().tolist()}")
    
    # Limpar formato brasileiro: remover pontos e trocar vírgula por ponto
    df_copy[coluna] = (df_copy[coluna]
                       .astype(str)
                       .str.replace('.', '', regex=False)
                       .str.replace(',', '.', regex=False)
                       .replace('nan', np.nan))
    
    # Converter para numérico
    df_copy[coluna] = pd.to_numeric(df_copy[coluna], errors='coerce')

    # Converter kg para toneladas
    if df_copy[coluna].max() > 100:
        df_copy[coluna] = df_copy[coluna] / 1000
        
    return df_copy

# Converter 'Quantidade de Carga 01' para toneladas
df = padronizar_unidades_para_toneladas(df, 'Quantidade de Carga 01')

print("\nPrimeiras linhas após conversão:")
print(df[['Quantidade de Carga 01']].head(10))