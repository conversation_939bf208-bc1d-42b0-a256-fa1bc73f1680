### 1. Importação das Bibliotecas e Dados do Dataloader

import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler
import dataloader

# Carregar os dados combinados do dataloader
train_data = dataloader.train_data
test_data = dataloader.test_data

print("Amostra dos dados de treino:")
print(train_data.head())

### 2. Tratamento de Missing Values

def tratar_missing_values(df):
    for col in df.select_dtypes(include=['float64', 'int64']).columns:
        df[col] = df[col].fillna(df[col].median())

    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].fillna("Desconhecido")
    return df

train_data = tratar_missing_values(train_data)
test_data = tratar_missing_values(test_data)

### 3. Normalização das Variáveis Numéricas

def normalizar_variaveis(df):
    scaler = StandardScaler()
    num_cols = df.select_dtypes(include=['float64', 'int64']).columns
    df[num_cols] = scaler.fit_transform(df[num_cols])
    return df

train_data = normalizar_variaveis(train_data)
test_data = normalizar_variaveis(test_data)

print("Pré-processamento concluído com sucesso.")
print("Dados de treino após pré-processamento:")
print(train_data.head())