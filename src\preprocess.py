### 1. Importação das Bibliotecas e Dados do Dataloader
import pandas as pd
import numpy as np
from sklearn.preprocessing import StandardScaler, RobustScaler
import dataloader

# Carregar os dados combinados do dataloader
train_data = dataloader.train_data.copy()
test_data = dataloader.test_data.copy()

print("Amostra dos dados de treino (brutos):")
print(train_data.head())

### 2. Tratamento de Missing Values

def tratar_missing_values(df):
    # Preencher numéricos com a mediana
    for col in df.select_dtypes(include=['float64', 'int64']).columns:
        df[col] = df[col].fillna(df[col].median())
    # Preencher categóricos com "Desconhecido"
    for col in df.select_dtypes(include=['object']).columns:
        df[col] = df[col].fillna("Desconhecido")
    return df

train_data = tratar_missing_values(train_data)
test_data = tratar_missing_values(test_data)

### 3. Tratamento de Outliers e Normalização

def escalar_variaveis(df, robust=True, standard=True):
    num_cols = df.select_dtypes(include=['float64', 'int64']).columns
    df_scaled = df.copy()

    if robust:
        print("\nAplicando RobustScaler (reduzir impacto de outliers)...")
        robust_scaler = RobustScaler()
        df_scaled[num_cols] = robust_scaler.fit_transform(df_scaled[num_cols])

    if standard:
        print("\nAplicando StandardScaler (distribuição normalizada)...")
        standard_scaler = StandardScaler()
        df_scaled[num_cols] = standard_scaler.fit_transform(df_scaled[num_cols])

    return df_scaled

train_data = escalar_variaveis(train_data, robust=True, standard=True)
test_data = escalar_variaveis(test_data, robust=True, standard=True)

### 4. Resultados após pré-processamento

print("\n Pré-processamento concluído com sucesso.")
print("Amostra de dados de treino após normalização:")
print(train_data.head())

print("\nColunas numéricas normalizadas:")
print(train_data.select_dtypes(include=['float64', 'int64']).columns.tolist())